<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API - Add Questions to Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .result {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .question-card {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test API - Add Questions to Test</h1>
        
        <!-- Auth Token Setup -->
        <div class="form-group">
            <label for="authToken">Auth Token:</label>
            <input type="text" id="authToken" placeholder="Enter your auth token here">
            <button onclick="setAuthToken()">Set Token</button>
        </div>

        <!-- Test Configuration -->
        <h3>📋 Test Configuration</h3>
        <div class="form-group">
            <label for="testId">Test ID:</label>
            <input type="text" id="testId" value="687f67966fad6a0ea16667e0">
        </div>
        
        <div class="form-group">
            <label for="questionIds">Question IDs (comma separated):</label>
            <input type="text" id="questionIds" value="687777f10b7ee9e83b238045" placeholder="question-id-1, question-id-2">
        </div>
        
        <div class="form-group">
            <label for="points">Points per Question:</label>
            <input type="number" id="points" value="5" min="1" max="10">
        </div>

        <!-- Action Buttons -->
        <h3>🚀 Actions</h3>
        <button onclick="addQuestionsToTest()">Add Questions to Test</button>
        <button onclick="getTestDetails()">Get Test Details</button>
        <button onclick="getAllTests()">Get All Tests</button>
        <button onclick="getQuestionsByCategory()">Get Frontend Questions</button>
        <button onclick="removeQuestionsFromTest()">Remove Questions</button>

        <!-- Results Display -->
        <div id="result"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:5000/api/tests';
        
        // Set auth token
        function setAuthToken() {
            const token = document.getElementById('authToken').value;
            if (token) {
                localStorage.setItem('authToken', token);
                showResult('✅ Auth token set successfully!', 'success');
            } else {
                showResult('❌ Please enter a valid token', 'error');
            }
        }

        // API Client
        async function apiRequest(url, options = {}) {
            const token = localStorage.getItem('authToken');
            
            const config = {
                headers: {
                    'Content-Type': 'application/json',
                    ...(token && { 'Authorization': `Bearer ${token}` })
                },
                credentials: 'include',
                ...options
            };

            try {
                const response = await fetch(`${API_BASE_URL}${url}`, config);
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.error || `HTTP error! status: ${response.status}`);
                }
                
                return data;
            } catch (error) {
                console.error('API Error:', error);
                throw error;
            }
        }

        // Add questions to test
        async function addQuestionsToTest() {
            try {
                const testId = document.getElementById('testId').value;
                const questionIdsInput = document.getElementById('questionIds').value;
                const points = parseInt(document.getElementById('points').value);

                if (!testId) {
                    throw new Error('Test ID is required');
                }

                if (!questionIdsInput) {
                    throw new Error('Question IDs are required');
                }

                // Parse question IDs
                const questionIds = questionIdsInput.split(',').map(id => id.trim()).filter(id => id);

                if (questionIds.length === 0) {
                    throw new Error('At least one question ID is required');
                }

                showResult('🔄 Adding questions to test...', 'info');

                const response = await apiRequest(`/${testId}/questions`, {
                    method: 'POST',
                    body: JSON.stringify({
                        questionIds: questionIds,
                        points: points
                    })
                });

                showResult(`✅ Success: ${response.message}\nTotal questions in test: ${response.totalQuestions}`, 'success');
            } catch (error) {
                showResult(`❌ Error: ${error.message}`, 'error');
            }
        }

        // Get test details
        async function getTestDetails() {
            try {
                const testId = document.getElementById('testId').value;
                if (!testId) {
                    throw new Error('Test ID is required');
                }

                showResult('🔄 Getting test details...', 'info');

                const response = await apiRequest(`/${testId}`);
                const test = response.test;

                const result = `✅ Test Details:
Name: ${test.testName}
Description: ${test.description}
Duration: ${test.duration} minutes
Total Points: ${test.totalPoints}
Questions: ${test.questions.length}
Participants: ${test.participants.length}
Created: ${new Date(test.createdAt).toLocaleString()}`;

                showResult(result, 'success');
            } catch (error) {
                showResult(`❌ Error: ${error.message}`, 'error');
            }
        }

        // Get all tests
        async function getAllTests() {
            try {
                showResult('🔄 Getting all tests...', 'info');

                const response = await apiRequest('/');
                const tests = response.tests;

                const result = `✅ Found ${tests.length} tests:
${tests.map(test => `- ${test.testName} (${test.questions.length} questions)`).join('\n')}`;

                showResult(result, 'success');
            } catch (error) {
                showResult(`❌ Error: ${error.message}`, 'error');
            }
        }

        // Get questions by category
        async function getQuestionsByCategory() {
            try {
                showResult('🔄 Getting Frontend questions...', 'info');

                const response = await apiRequest('/questions/by-category?category=Frontend');
                const questions = response.questions;

                const result = `✅ Found ${questions.length} Frontend questions:
${questions.map(q => `- ${q.questionText.substring(0, 50)}... (${q.difficulty}, ${q.points} pts)`).join('\n')}`;

                showResult(result, 'success');
            } catch (error) {
                showResult(`❌ Error: ${error.message}`, 'error');
            }
        }

        // Remove questions from test
        async function removeQuestionsFromTest() {
            try {
                const testId = document.getElementById('testId').value;
                const questionIdsInput = document.getElementById('questionIds').value;

                if (!testId) {
                    throw new Error('Test ID is required');
                }

                if (!questionIdsInput) {
                    throw new Error('Question IDs are required');
                }

                const questionIds = questionIdsInput.split(',').map(id => id.trim()).filter(id => id);

                if (questionIds.length === 0) {
                    throw new Error('At least one question ID is required');
                }

                if (!confirm(`Are you sure you want to remove ${questionIds.length} questions from the test?`)) {
                    return;
                }

                showResult('🔄 Removing questions from test...', 'info');

                const response = await apiRequest(`/${testId}/questions`, {
                    method: 'DELETE',
                    body: JSON.stringify({
                        questionIds: questionIds
                    })
                });

                showResult(`✅ Success: ${response.message}\nTotal questions in test: ${response.totalQuestions}`, 'success');
            } catch (error) {
                showResult(`❌ Error: ${error.message}`, 'error');
            }
        }

        // Show result
        function showResult(message, type = 'info') {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `<div class="${type}">${message}</div>`;
        }

        // Initialize
        window.onload = function() {
            const savedToken = localStorage.getItem('authToken');
            if (savedToken) {
                document.getElementById('authToken').value = savedToken;
                showResult('✅ Auth token loaded from localStorage', 'success');
            } else {
                showResult('⚠️ Please set your auth token first', 'error');
            }
        };
    </script>
</body>
</html>
