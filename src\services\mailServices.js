const nodemailer = require("nodemailer");

const transporter = nodemailer.createTransport({
  host: "smtp.gmail.com",
  port: 587,
  secure: false,
  auth: {
    user: "<EMAIL>",
    pass: "towb ekvx nott fzbd",
  },
});

const LOGO_URL = "https://res.cloudinary.com/dhqwyqekj/image/upload/w_1000,c_fill,ar_1:1,g_auto,r_max,bo_5px_solid_red,b_rgb:262c35/v1750831228/Medini_logo_White-1_1_kfnj8n.png";
const BRAND_NAME = "Resumeduilder";

function createEmailTemplate({ title, message, buttonUrl, buttonText }) {
  return `
<!DOCTYPE html>
<html>
<body style="margin:0;padding:0;background-color:#f9f9f9;">
<table role="presentation" width="100%" cellpadding="0" cellspacing="0" style="font-family: Arial, sans-serif;">
<tr><td align="center">
  <table role="presentation" width="600" cellpadding="0" cellspacing="0" style="background-color:#ffffff;border-radius:8px;overflow:hidden;">
    <tr>
      <td align="center" style="padding:30px;">
        <img src="${LOGO_URL}" alt="${BRAND_NAME}" style="width:120px;height:auto;" />
      </td>
    </tr>
    <tr>
      <td align="center" style="padding:0 30px 20px 30px;">
        <h2 style="color:#29354d;font-size:24px;margin:0;">${title}</h2>
      </td>
    </tr>
    <tr>
      <td align="center" style="padding:0 30px;">
        <p style="color:#555;font-size:16px;line-height:1.5;margin:0;">
            ${message}
        </p>
      </td>
    </tr>
    ${buttonUrl && buttonText
      ? `
    <tr>
      <td align="center" style="padding:30px;">
        <a href="${buttonUrl}" style="
            background-color:#fcc250;
            color:#29354d;
            font-weight:bold;
            font-size:16px;
            text-decoration:none;
            padding:12px 30px;
            border-radius:6px;
            display:inline-block;">
            ${buttonText}
        </a>
      </td>
    </tr>
    `
      : ""
    }
    <tr>
      <td align="center" style="padding:30px;">
        <hr style="border:none;border-bottom:1px solid #ccc;" />
        <p style="font-size:14px;color:#555;text-align:center;">
            Thanks,<br/>The ${BRAND_NAME} Team
        </p>
      </td>
    </tr>
  </table>
</td></tr>
</table>
</body>
</html>`;
}

async function sendEmail({ to, subject, title, message, buttonUrl, buttonText }) {
  const html = createEmailTemplate({ title, message, buttonUrl, buttonText });
  const text = `${title}\n\n${message}${buttonUrl ? `\n\nLink: ${buttonUrl}` : ""}`;

  return transporter.sendMail({
    from: `"Support ${BRAND_NAME}" <<EMAIL>>`,
    to,
    subject,
    text,
    html,
  });
}

async function sendPasswordResetEmail(toEmail, resetLink) {
  return sendEmail({
    to: toEmail,
    subject: "Password Reset Request",
    title: "Password Reset Request",
    message:
      "We received a request to reset your password. Click the button below to set a new one. This link will expire in 1 hour.",
    buttonUrl: resetLink,
    buttonText: "Reset Password",
  });
}

async function sendWelcomeEmail(toEmail, userName) {
  return sendEmail({
    to: toEmail,
    subject: "Welcome to Resumeduilder!",
    title: `Welcome, ${userName}!`,
    message:
      "Thank you for joining Resumeduilder. We’re excited to have you on board. Get started by exploring the site and creating your first resume.",
    buttonUrl: "https://yourapp.com",
    buttonText: "Get Started",
  });
}

async function sendNotificationEmail(toEmail, subject, message) {
  return sendEmail({
    to: toEmail,
    subject,
    title: subject,
    message,
  });
}
async function sendOtpEmail(toEmail, otp) {
  const verificationLink = `http://localhost:5173/verify-otp?email=${encodeURIComponent(toEmail)}&otp=${otp}`;

  return sendEmail({
    to: toEmail,
    subject: "Email Verification Link",
    title: "Verify Your Email Address",
    message: `
      <p>Hello,</p>
      <p>Please verify your email by clicking the link below:</p>
      <p><a href="${verificationLink}" target="_blank" style="color: #2563eb; font-weight: bold;">Verify Email</a></p>
      <p>Or use the following OTP: <strong>${otp}</strong></p>
      <p>This link and OTP will expire in 10 minutes.</p>
    `,
  });
}

module.exports = {
  sendPasswordResetEmail,
  sendWelcomeEmail,
  sendNotificationEmail,
  sendOtpEmail
};
