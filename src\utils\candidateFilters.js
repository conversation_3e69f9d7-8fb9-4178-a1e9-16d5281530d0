/**
 * Utility functions for filtering candidates based on resume data
 */

/**
 * Build MongoDB match conditions for resume-based filtering
 */
const buildResumeFilters = (filters) => {
    const {
        skills,
        location,
        education,
        languages,
        certifications,
        searchTerm
    } = filters;

    const matchConditions = {};

    // Skills filtering
    if (skills) {
        const skillsArray = skills.split(',').map(s => s.trim());
        matchConditions['resumeData.Skills.Skill'] = {
            $in: skillsArray.map(skill => new RegExp(skill, 'i'))
        };
    }

    // Location filtering
    if (location) {
        matchConditions['resumeData.Location'] = new RegExp(location, 'i');
    }

    // Education filtering
    if (education) {
        matchConditions['resumeData.Education.Degree'] = new RegExp(education, 'i');
    }

    // Languages filtering
    if (languages) {
        const languagesArray = languages.split(',').map(l => l.trim());
        matchConditions['resumeData.Languages.Name'] = {
            $in: languagesArray.map(lang => new RegExp(lang, 'i'))
        };
    }

    // Certifications filtering
    if (certifications) {
        matchConditions['resumeData.Certifications.Title'] = new RegExp(certifications, 'i');
    }

    // Search term filtering (searches across multiple fields)
    if (searchTerm) {
        matchConditions.$or = [
            { 'candidateUser.name': new RegExp(searchTerm, 'i') },
            { 'candidateUser.email': new RegExp(searchTerm, 'i') },
            { 'resumeData.Title': new RegExp(searchTerm, 'i') },
            { 'resumeData.summery': new RegExp(searchTerm, 'i') },
            { 'resumeData.Headline': new RegExp(searchTerm, 'i') },
            { 'title': new RegExp(searchTerm, 'i') } // Job title
        ];
    }

    return matchConditions;
};

/**
 * Build experience level filtering pipeline stages
 */
const buildExperienceFilters = (filters) => {
    const { experienceLevel, minExperience, maxExperience } = filters;
    
    if (!experienceLevel && !minExperience && !maxExperience) {
        return [];
    }

    const stages = [];

    // Add field to calculate total experience
    stages.push({
        $addFields: {
            totalExperience: {
                $reduce: {
                    input: '$resumeData.Experience',
                    initialValue: 0,
                    in: {
                        $add: [
                            '$$value',
                            {
                                $divide: [
                                    {
                                        $subtract: [
                                            { $ifNull: ['$$this.EndDate', new Date()] },
                                            '$$this.StartDate'
                                        ]
                                    },
                                    365.25 * 24 * 60 * 60 * 1000 // Convert to years
                                ]
                            }
                        ]
                    }
                }
            }
        }
    });

    // Build experience conditions
    const expConditions = {};
    
    if (minExperience) {
        expConditions.totalExperience = { $gte: parseFloat(minExperience) };
    }
    
    if (maxExperience) {
        expConditions.totalExperience = { 
            ...expConditions.totalExperience,
            $lte: parseFloat(maxExperience) 
        };
    }
    
    if (experienceLevel) {
        const expLevelMap = {
            'entry': { $lt: 2 },
            'mid': { $gte: 2, $lt: 5 },
            'senior': { $gte: 5, $lt: 10 },
            'lead': { $gte: 10 }
        };
        if (expLevelMap[experienceLevel.toLowerCase()]) {
            expConditions.totalExperience = expLevelMap[experienceLevel.toLowerCase()];
        }
    }

    if (Object.keys(expConditions).length > 0) {
        stages.push({ $match: expConditions });
    }

    return stages;
};

/**
 * Build sorting pipeline stage
 */
const buildSortStage = (sortBy, sortOrder) => {
    const sortField = sortBy === 'appliedAt' ? 'application.appliedAt' : 
                     sortBy === 'name' ? 'candidate.name' :
                     sortBy === 'experience' ? 'totalExperience' :
                     sortBy === 'jobTitle' ? 'jobTitle' : 'application.appliedAt';
    
    return {
        $sort: { [sortField]: sortOrder === 'desc' ? -1 : 1 }
    };
};

/**
 * Build pagination pipeline stages
 */
const buildPaginationStages = (page, limit) => {
    return [
        { $skip: (page - 1) * parseInt(limit) },
        { $limit: parseInt(limit) }
    ];
};

/**
 * Build base aggregation pipeline for candidate lookup
 */
const buildBaseCandidatePipeline = (companyId, jobId = null) => {
    return [
        // Match jobs for this company
        {
            $match: {
                companyId,
                ...(jobId ? { _id: jobId } : {})
            }
        },
        // Unwind applicants array
        {
            $unwind: {
                path: '$applicants',
                preserveNullAndEmptyArrays: false
            }
        },
        // Lookup user data
        {
            $lookup: {
                from: 'users',
                localField: 'applicants.candidateId',
                foreignField: '_id',
                as: 'candidateUser'
            }
        },
        {
            $unwind: {
                path: '$candidateUser',
                preserveNullAndEmptyArrays: true
            }
        },
        // Lookup resume data using email
        {
            $lookup: {
                from: 'resumeprofiles',
                localField: 'candidateUser.email',
                foreignField: 'Email',
                as: 'resumeData'
            }
        },
        {
            $unwind: {
                path: '$resumeData',
                preserveNullAndEmptyArrays: true
            }
        }
    ];
};

/**
 * Build project stage for final output structure
 */
const buildProjectStage = (includeJobInfo = true) => {
    const baseProject = {
        _id: 0,
        application: {
            candidateId: '$applicants.candidateId',
            appliedAt: '$applicants.appliedAt',
            status: '$applicants.status',
            testScore: '$applicants.testScore'
        },
        candidate: {
            name: '$candidateUser.name',
            email: '$candidateUser.email',
            userId: '$candidateUser._id'
        },
        resume: '$resumeData',
        totalExperience: { $ifNull: ['$totalExperience', 0] }
    };

    if (includeJobInfo) {
        baseProject.jobId = '$_id';
        baseProject.jobTitle = '$title';
        baseProject.jobCategory = '$category';
        baseProject.jobLocation = '$location';
    }

    return { $project: baseProject };
};

/**
 * Validate filter parameters
 */
const validateFilters = (filters) => {
    const errors = [];
    
    if (filters.minExperience && isNaN(parseFloat(filters.minExperience))) {
        errors.push('minExperience must be a valid number');
    }
    
    if (filters.maxExperience && isNaN(parseFloat(filters.maxExperience))) {
        errors.push('maxExperience must be a valid number');
    }
    
    if (filters.minExperience && filters.maxExperience && 
        parseFloat(filters.minExperience) > parseFloat(filters.maxExperience)) {
        errors.push('minExperience cannot be greater than maxExperience');
    }
    
    if (filters.experienceLevel && 
        !['entry', 'mid', 'senior', 'lead'].includes(filters.experienceLevel.toLowerCase())) {
        errors.push('experienceLevel must be one of: entry, mid, senior, lead');
    }
    
    if (filters.sortBy && 
        !['appliedAt', 'name', 'experience', 'jobTitle'].includes(filters.sortBy)) {
        errors.push('sortBy must be one of: appliedAt, name, experience, jobTitle');
    }
    
    if (filters.sortOrder && !['asc', 'desc'].includes(filters.sortOrder)) {
        errors.push('sortOrder must be either asc or desc');
    }
    
    return errors;
};

module.exports = {
    buildResumeFilters,
    buildExperienceFilters,
    buildSortStage,
    buildPaginationStages,
    buildBaseCandidatePipeline,
    buildProjectStage,
    validateFilters
};
