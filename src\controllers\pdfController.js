const puppeteer = require("puppeteer");

exports.generatePDF = async (req, res) => {
  const { html, fileName = "resume", format = "pdf" } = req.body;

  if (!html) {
    return res.status(400).json({ error: "Missing HTML content" });
  }

  // Wrap HTML content with full HTML structure
  const fullHTML = `
    <!DOCTYPE html>
    <html lang="en">
      <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>${fileName}</title>

        <!-- Tailwind CSS -->
        <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet" />

        <!-- Google Fonts -->
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet" />

        <style>
          html, body {
            font-family: 'Inter', sans-serif;
            padding: 0;
            margin: 0;
            background-color: white !important;  /* ✅ Fixed yellow background issue */
            color: #29354d;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
          }

          * {
            box-sizing: border-box;
          }

          @page {
            size: A4;
            margin: 20px;
          }

          img {
            max-width: 100%;
            height: auto;
          }
        </style>
      </head>
      <body>
        ${html}
      </body>
    </html>
  `;

  try {
    const browser = await puppeteer.launch({
      headless: "new",
      args: ["--no-sandbox", "--disable-setuid-sandbox"],
    });

    const page = await browser.newPage();

    // Set content and wait for full load
    await page.setContent(fullHTML, { waitUntil: "networkidle0" });

    // Use screen media type to respect screen styles
    await page.emulateMediaType("screen");

    // Handle PNG or JPEG export
    if (format === "png" || format === "jpeg") {
      const buffer = await page.screenshot({
        fullPage: true,
        type: format,
      });

      await browser.close();

      res.set({
        "Content-Type": `image/${format}`,
        "Content-Disposition": `attachment; filename="${fileName}.${format}"`,
      });

      return res.send(buffer);
    }

    // Default: Export as PDF
    const pdfBuffer = await page.pdf({
      format: "A4",
      printBackground: true,
      preferCSSPageSize: true, // ✅ Helps remove extra yellow space
      margin: {
        top: "20px",
        bottom: "20px",
        left: "20px",
        right: "20px",
      },
    });

    await browser.close();

    res.set({
      "Content-Type": "application/pdf",
      "Content-Disposition": `attachment; filename="${fileName}.pdf"`,
    });

    return res.send(pdfBuffer);
  } catch (error) {
    console.error("PDF Generation Error:", error);
    return res.status(500).json({ error: "PDF generation failed" });
  }
};
