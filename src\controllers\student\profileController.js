const ResumeProfile = require('../../models/UserResumeDB');
const User = require('../../models/User');
const cloudinary = require('cloudinary').v2;
const fs = require('fs');

// Constants
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB

/**
 //* Helper: Get profile by user email
 */
const findProfileOrFail = async (email, res) => {
    const profile = await ResumeProfile.findOne({ Email: email });
    if (!profile) {
        res.status(404).json({
            success: false,
            message: 'Profile not found. Please create your profile first.'
        });
        return null;
    }
    return profile;
};

const getProfile = async (req, res) => {
    try {
        const { email } = req.user;
        const profile = await ResumeProfile.findOne({ Email: email });

        if (!profile) {
            return res.status(404).json({
                success: false,
                message: 'Profile not found. Please create your profile first.'
            });
        }

        res.json({
            success: true,
            data: {
                profile,
                completionStatus: calculateProfileCompletion(profile)
            }
        });

    } catch (error) {
        console.error('Error fetching profile:', error);
        res.status(500).json({ success: false, message: 'Server error', error: error.message });
    }
};


const updateProfile = async (req, res) => {
    try {
        const { email } = req.user;
        const updateData = req.body;
        let profile = await ResumeProfile.findOne({ Email: email });

        if (!profile) {
            profile = new ResumeProfile({ Email: email, Title: updateData.Title || 'My Resume', ...updateData });
        } else {
            Object.assign(profile, updateData);
        }

        await profile.save();

        res.json({
            success: true,
            message: 'Profile updated successfully',
            data: {
                profile,
                completionStatus: calculateProfileCompletion(profile)
            }
        });

    } catch (error) {
        console.error('Error updating profile:', error);
        if (error.code === 11000) {
            return res.status(400).json({ success: false, message: 'Profile with this title already exists' });
        }

        res.status(500).json({ success: false, message: 'Error updating profile', error: error.message });
    }
};


const updateSkills = async (req, res) => {
    try {
        const { email } = req.user;
        const { skills, action = 'replace' } = req.body;

        if (!Array.isArray(skills)) {
            return res.status(400).json({ success: false, message: 'Skills must be an array' });
        }

        const validSkills = skills.filter(skill => skill.Skill && skill.Proficiency);
        if (validSkills.length !== skills.length) {
            return res.status(400).json({ success: false, message: 'Each skill must have Skill and Proficiency' });
        }

        const profile = await findProfileOrFail(email, res);
        if (!profile) return;

        switch (action) {
            case 'replace':
                profile.Skills = validSkills;
                break;
            case 'add':
                profile.Skills.push(...validSkills);
                break;
            case 'remove':
                const skillsToRemove = validSkills.map(s => s.Skill);
                profile.Skills = profile.Skills.filter(skill => !skillsToRemove.includes(skill.Skill));
                break;
            default:
                return res.status(400).json({ success: false, message: 'Invalid action. Use replace, add, or remove' });
        }

        await profile.save();

        res.json({
            success: true,
            message: 'Skills updated successfully',
            data: {
                skills: profile.Skills,
                totalSkills: profile.Skills.length
            }
        });

    } catch (error) {
        console.error('Error updating skills:', error);
        res.status(500).json({ success: false, message: 'Error updating skills', error: error.message });
    }
};


const updateEducation = async (req, res) => {
    try {
        const { email } = req.user;
        const { education, action = 'replace' } = req.body;

        if (!Array.isArray(education)) {
            return res.status(400).json({ success: false, message: 'Education must be an array' });
        }

        const validEducation = education.filter(edu => edu.Institution && edu.Degree && edu.StartDate);
        if (validEducation.length !== education.length) {
            return res.status(400).json({
                success: false,
                message: 'Each education entry must have Institution, Degree, and StartDate'
            });
        }

        const profile = await findProfileOrFail(email, res);
        if (!profile) return;

        switch (action) {
            case 'replace':
                profile.Education = validEducation;
                break;
            case 'add':
                profile.Education.push(...validEducation);
                break;
            case 'remove':
                const toRemove = validEducation.map(edu => `${edu.Institution}-${edu.Degree}`);
                profile.Education = profile.Education.filter(edu =>
                    !toRemove.includes(`${edu.Institution}-${edu.Degree}`)
                );
                break;
            default:
                return res.status(400).json({ success: false, message: 'Invalid action. Use replace, add, or remove' });
        }

        await profile.save();

        res.json({
            success: true,
            message: 'Education updated successfully',
            data: {
                education: profile.Education,
                totalEducation: profile.Education.length
            }
        });

    } catch (error) {
        console.error('Error updating education:', error);
        res.status(500).json({ success: false, message: 'Error updating education', error: error.message });
    }
};


const uploadResume = async (req, res) => {
    try {
        const { email } = req.user;
        if (!req.file) {
            return res.status(400).json({ success: false, message: 'No file uploaded' });
        }
        if (req.file.mimetype !== 'application/pdf') {
            fs.unlinkSync(req.file.path);
            return res.status(400).json({ success: false, message: 'Only PDF files are allowed' });
        }
        if (req.file.size > MAX_FILE_SIZE) {
            fs.unlinkSync(req.file.path);
            return res.status(400).json({ success: false, message: 'File size must be under 5MB' });
        }
        const profile = await findProfileOrFail(email, res);
        if (!profile) {
            fs.unlinkSync(req.file.path);
            return;
        }
        // Upload to cloudinary
        const result = await cloudinary.uploader.upload(req.file.path, {
            folder: 'resumes',
            resource_type: 'raw',
            public_id: `resume_${email.replace('@', '_')}_${Date.now()}`
        });
        // Delete old resume if exists
        if (profile.ProfileImagePublicId) {
            try {
                await cloudinary.uploader.destroy(profile.ProfileImagePublicId, { resource_type: 'raw' });
            } catch (deleteError) {
                console.warn('Cloudinary resume deletion failed:', deleteError.message);
            }
        }

        profile.PDFLink = result.secure_url;
        profile.ProfileImagePublicId = result.public_id;
        profile.LastExported = new Date();
        await profile.save();

        fs.unlinkSync(req.file.path); // Cleanup local file

        res.json({
            success: true,
            message: 'Resume uploaded successfully',
            data: {
                resumeUrl: result.secure_url,
                uploadedAt: new Date(),
                publicId: result.public_id,
                fileSize: req.file.size,
                originalName: req.file.originalname
            }
        });

    } catch (error) {
        console.error('Error uploading resume:', error);
        if (req.file?.path) {
            try {
                fs.unlinkSync(req.file.path);
            } catch (cleanupError) {
                console.warn('Cleanup error:', cleanupError.message);
            }
        }
        res.status(500).json({ success: false, message: 'Upload failed', error: error.message });
    }
};


const calculateProfileCompletion = (profile) => {
    const fields = ['Title', 'summary', 'Email', 'Phone', 'Location', 'Headline', 'Experience', 'Education', 'Skills', 'Projects'];
    const completed = fields.filter(key => Array.isArray(profile[key]) ? profile[key].length : !!profile[key]?.toString().trim());
    return Math.round((completed.length / fields.length) * 100);
};

module.exports = {
    getProfile,
    updateProfile,
    updateSkills,
    updateEducation,
    uploadResume
};


