const mongoose = require('mongoose');

const connectDB = async () => {
  try {
    console.log('Trying to connect to MongoDB with URI:', process.env.DATABASE_URL);
    const conn = await mongoose.connect(process.env.DATABASE_URL, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log(`MongoDB Connected: ${conn.connection.host}`);
  } catch (error) {
    console.error('MongoDB connection failed:', error);
    process.exit(1);
  }
};
 
module.exports = connectDB;