// ! Auth Routes ---------------->
const express = require('express')
const router = express.Router()
const passport = require('passport')
const authController = require('../controllers/authController')
const authMiddleware = require('../middlewares/authMiddleware')
const validateRequest = require('../middlewares/validateRequest')
const { loginSchema, registerSchema } = require('../utils/validators')

router.post('/register', validateRequest(registerSchema), authController.register)
router.post("/verify-otp", authController.verifyOtp);

router.post("/resend-otp", authController.resendOtp);

router.post('/login', validateRequest(loginSchema), authController.login)

router.get('/me', authMiddleware, authController.getCurrentUser) // 
// ! Logout
router.post('/logout', authController.logout)

// !Reset password
router.post('/forgot-password', authController.forgotPassword)
router.get('/reset-password/:token', authController.verifyResetToken)
router.post('/reset-password', authController.resetPassword)
router.put("/update", authMiddleware, authController.updateUser);

// *Redirect to Google
router.get('/google', passport.authenticate('google', { scope: ['profile', 'email'] }))

// * Google callback
router.get('/google/callback', passport.authenticate('google', { session: false }), authController.oauthCallback)

// * GitHub login placeholder
router.get('/github', authController.githubLogin)

module.exports = router
