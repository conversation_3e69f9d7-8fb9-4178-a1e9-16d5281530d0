const {
    buildResumeFilters,
    buildExperienceFilters,
    buildSortStage,
    buildPaginationStages,
    buildBaseCandidatePipeline,
    buildProjectStage,
    validateFilters
} = require('../utils/candidateFilters');
const mongoose = require('mongoose');

describe('Candidate Filtering Utilities', () => {
    describe('buildResumeFilters', () => {
        it('should build skills filter correctly', () => {
            const filters = { skills: 'JavaScript,React,Node.js' };
            const result = buildResumeFilters(filters);
            
            expect(result).toHaveProperty('resumeData.Skills.Skill');
            expect(result['resumeData.Skills.Skill'].$in).toHaveLength(3);
        });

        it('should build location filter correctly', () => {
            const filters = { location: 'New York' };
            const result = buildResumeFilters(filters);
            
            expect(result).toHaveProperty('resumeData.Location');
            expect(result['resumeData.Location']).toBeInstanceOf(RegExp);
        });

        it('should build education filter correctly', () => {
            const filters = { education: 'Computer Science' };
            const result = buildResumeFilters(filters);
            
            expect(result).toHaveProperty('resumeData.Education.Degree');
        });

        it('should build search term filter correctly', () => {
            const filters = { searchTerm: 'developer' };
            const result = buildResumeFilters(filters);
            
            expect(result).toHaveProperty('$or');
            expect(result.$or).toHaveLength(6);
        });

        it('should return empty object for no filters', () => {
            const filters = {};
            const result = buildResumeFilters(filters);
            
            expect(Object.keys(result)).toHaveLength(0);
        });
    });

    describe('buildExperienceFilters', () => {
        it('should build experience level filter for entry level', () => {
            const filters = { experienceLevel: 'entry' };
            const result = buildExperienceFilters(filters);
            
            expect(result).toHaveLength(2);
            expect(result[0]).toHaveProperty('$addFields');
            expect(result[1]).toHaveProperty('$match');
        });

        it('should build min/max experience filter', () => {
            const filters = { minExperience: '2', maxExperience: '5' };
            const result = buildExperienceFilters(filters);
            
            expect(result).toHaveLength(2);
            expect(result[1].$match.totalExperience).toHaveProperty('$gte', 2);
            expect(result[1].$match.totalExperience).toHaveProperty('$lte', 5);
        });

        it('should return empty array for no experience filters', () => {
            const filters = {};
            const result = buildExperienceFilters(filters);
            
            expect(result).toHaveLength(0);
        });
    });

    describe('buildSortStage', () => {
        it('should build sort stage for appliedAt desc', () => {
            const result = buildSortStage('appliedAt', 'desc');
            
            expect(result).toHaveProperty('$sort');
            expect(result.$sort['application.appliedAt']).toBe(-1);
        });

        it('should build sort stage for name asc', () => {
            const result = buildSortStage('name', 'asc');
            
            expect(result).toHaveProperty('$sort');
            expect(result.$sort['candidate.name']).toBe(1);
        });

        it('should default to appliedAt for unknown sort field', () => {
            const result = buildSortStage('unknown', 'desc');
            
            expect(result.$sort['application.appliedAt']).toBe(-1);
        });
    });

    describe('buildPaginationStages', () => {
        it('should build pagination stages correctly', () => {
            const result = buildPaginationStages(2, 10);
            
            expect(result).toHaveLength(2);
            expect(result[0]).toHaveProperty('$skip', 10);
            expect(result[1]).toHaveProperty('$limit', 10);
        });

        it('should handle first page correctly', () => {
            const result = buildPaginationStages(1, 5);
            
            expect(result[0].$skip).toBe(0);
            expect(result[1].$limit).toBe(5);
        });
    });

    describe('buildBaseCandidatePipeline', () => {
        const companyId = new mongoose.Types.ObjectId();
        const jobId = new mongoose.Types.ObjectId();

        it('should build base pipeline without job filter', () => {
            const result = buildBaseCandidatePipeline(companyId);
            
            expect(result).toHaveLength(6);
            expect(result[0].$match.companyId).toBe(companyId);
            expect(result[0].$match).not.toHaveProperty('_id');
        });

        it('should build base pipeline with job filter', () => {
            const result = buildBaseCandidatePipeline(companyId, jobId);
            
            expect(result[0].$match.companyId).toBe(companyId);
            expect(result[0].$match._id).toBe(jobId);
        });

        it('should include all necessary lookup stages', () => {
            const result = buildBaseCandidatePipeline(companyId);
            
            const lookupStages = result.filter(stage => stage.$lookup);
            expect(lookupStages).toHaveLength(2);
            
            expect(lookupStages[0].$lookup.from).toBe('users');
            expect(lookupStages[1].$lookup.from).toBe('resumeprofiles');
        });
    });

    describe('buildProjectStage', () => {
        it('should build project stage with job info', () => {
            const result = buildProjectStage(true);
            
            expect(result.$project).toHaveProperty('jobId');
            expect(result.$project).toHaveProperty('jobTitle');
            expect(result.$project).toHaveProperty('candidate');
            expect(result.$project).toHaveProperty('resume');
        });

        it('should build project stage without job info', () => {
            const result = buildProjectStage(false);
            
            expect(result.$project).not.toHaveProperty('jobId');
            expect(result.$project).not.toHaveProperty('jobTitle');
            expect(result.$project).toHaveProperty('candidate');
            expect(result.$project).toHaveProperty('resume');
        });
    });

    describe('validateFilters', () => {
        it('should validate correct filters', () => {
            const filters = {
                minExperience: '2',
                maxExperience: '5',
                experienceLevel: 'mid',
                sortBy: 'name',
                sortOrder: 'asc'
            };
            
            const errors = validateFilters(filters);
            expect(errors).toHaveLength(0);
        });

        it('should catch invalid experience values', () => {
            const filters = {
                minExperience: 'invalid',
                maxExperience: 'also-invalid'
            };
            
            const errors = validateFilters(filters);
            expect(errors).toHaveLength(2);
            expect(errors[0]).toContain('minExperience must be a valid number');
            expect(errors[1]).toContain('maxExperience must be a valid number');
        });

        it('should catch min > max experience', () => {
            const filters = {
                minExperience: '5',
                maxExperience: '2'
            };
            
            const errors = validateFilters(filters);
            expect(errors).toHaveLength(1);
            expect(errors[0]).toContain('minExperience cannot be greater than maxExperience');
        });

        it('should catch invalid experience level', () => {
            const filters = { experienceLevel: 'invalid' };
            
            const errors = validateFilters(filters);
            expect(errors).toHaveLength(1);
            expect(errors[0]).toContain('experienceLevel must be one of');
        });

        it('should catch invalid sort parameters', () => {
            const filters = {
                sortBy: 'invalid',
                sortOrder: 'invalid'
            };
            
            const errors = validateFilters(filters);
            expect(errors).toHaveLength(2);
            expect(errors[0]).toContain('sortBy must be one of');
            expect(errors[1]).toContain('sortOrder must be either asc or desc');
        });
    });

    describe('Edge Cases', () => {
        it('should handle empty strings in filters', () => {
            const filters = {
                skills: '',
                location: '',
                searchTerm: ''
            };
            
            const result = buildResumeFilters(filters);
            expect(Object.keys(result)).toHaveLength(0);
        });

        it('should handle whitespace-only skills', () => {
            const filters = { skills: '  ,  ,  ' };
            const result = buildResumeFilters(filters);
            
            // Should still create filter but with empty strings
            expect(result).toHaveProperty('resumeData.Skills.Skill');
        });

        it('should handle case-insensitive experience levels', () => {
            const filters = { experienceLevel: 'SENIOR' };
            const result = buildExperienceFilters(filters);
            
            expect(result).toHaveLength(2);
            expect(result[1].$match.totalExperience).toHaveProperty('$gte', 5);
        });
    });
});
