const mongoose = require('mongoose');

const QuestionBundleSchema = new mongoose.Schema({
    companyId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Company',
        required: true,
        index: true
    },
    createdBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    updatedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    },
    bundleName: {
        type: String,
        required: true,
        trim: true,
        maxlength: 200
    },
    description: {
        type: String,
        maxlength: 500,
        trim: true
    },
    category: {
        type: String,
        enum: ['Frontend', 'Backend', 'Full Stack', 'Data Science', 'DevOps', 'Mobile', 'UI/UX', 'QA', 'Aptitude', 'Logical', 'Other'],
        required: true
    },
    difficulty: {
        type: String,
        enum: ['Easy', 'Medium', 'Hard', 'Mixed'],
        default: 'Mixed'
    },
    questions: [{
        questionId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Question',
            required: true
        },
        points: {
            type: Number,
            default: 1,
            min: 1,
            max: 10
        },
        order: {
            type: Number,
            default: 0
        }
    }],
    totalQuestions: {
        type: Number,
        default: 0
    },
    totalPoints: {
        type: Number,
        default: 0
    },
    estimatedDuration: {
        type: Number, // in minutes
        default: 0
    },
    tags: [{
        type: String,
        trim: true,
        maxlength: 50
    }],
    isActive: {
        type: Boolean,
        default: true
    },
    isPublic: {
        type: Boolean,
        default: false // Only visible to the company that created it
    },
    usageCount: {
        type: Number,
        default: 0
    },
    lastUsedAt: {
        type: Date,
        default: null
    }
}, {
    timestamps: true
});

// Indexes for better performance
QuestionBundleSchema.index({ companyId: 1, category: 1 });
QuestionBundleSchema.index({ companyId: 1, isActive: 1 });
QuestionBundleSchema.index({ bundleName: 'text', description: 'text', tags: 'text' });

// Pre-save middleware to calculate totals
QuestionBundleSchema.pre('save', function(next) {
    if (this.questions && this.questions.length > 0) {
        this.totalQuestions = this.questions.length;
        this.totalPoints = this.questions.reduce((sum, q) => sum + (q.points || 1), 0);
        // Estimate 1.5 minutes per question on average
        this.estimatedDuration = Math.ceil(this.totalQuestions * 1.5);
    } else {
        this.totalQuestions = 0;
        this.totalPoints = 0;
        this.estimatedDuration = 0;
    }
    next();
});

// Method to increment usage count
QuestionBundleSchema.methods.incrementUsage = function() {
    this.usageCount += 1;
    this.lastUsedAt = new Date();
    return this.save();
};

module.exports = mongoose.model('QuestionBundle', QuestionBundleSchema);
