const ResumeProfile = require('../models/UserResumeDB');

exports.testAPI = (req, res) => {
  res.send('Resume delete API is working');
};

const deleteByIdFromResume = async (res, resumeId, field, subDocId, label) => {
  try {
    const updated = await ResumeProfile.findOneAndUpdate(
      { _id: resumeId },
      { $pull: { [field]: { _id: subDocId } } },
      { new: true }
    );
    if (!updated) return res.status(404).json({ error: 'Resume not found' });
    res.status(200).json({ message: `${label} deleted successfully`, updated });
  } catch (err) {
    res.status(500).json({ error: 'Internal server error', details: err.message });
  }
};

exports.deleteProfile = (req, res) => {
  deleteByIdFromResume(res, req.params.resumeId, 'Profiles', req.params.id, 'Profile');
};

exports.deleteExperience = (req, res) => {
  deleteByIdFromResume(res, req.params.resumeId, 'Experience', req.params.id, 'Experience');
};

exports.deleteEducation = (req, res) => {
  deleteByIdFromResume(res, req.params.resumeId, 'Education', req.params.id, 'Education');
};

exports.deleteSkill = (req, res) => {
  deleteByIdFromResume(res, req.params.resumeId, 'Skills', req.params.id, 'Skill');
};

exports.deleteLanguage = (req, res) => {
  deleteByIdFromResume(res, req.params.resumeId, 'Languages', req.params.id, 'Language');
};

exports.deleteAward = (req, res) => {
  deleteByIdFromResume(res, req.params.resumeId, 'Awards', req.params.id, 'Award');
};

exports.deleteCertification = (req, res) => {
  deleteByIdFromResume(res, req.params.resumeId, 'Certifications', req.params.id, 'Certification');
};

exports.deleteInterest = (req, res) => {
  deleteByIdFromResume(res, req.params.resumeId, 'Interests', req.params.id, 'Interest');
};

exports.deletePublication = (req, res) => {
  deleteByIdFromResume(res, req.params.resumeId, 'Publications', req.params.id, 'Publication');
};

exports.deleteVolunteering = (req, res) => {
  deleteByIdFromResume(res, req.params.resumeId, 'Volunteering', req.params.id, 'Volunteering');
};

exports.deleteReference = (req, res) => {
  deleteByIdFromResume(res, req.params.resumeId, 'References', req.params.id, 'Reference');
};
exports.deleteSummary = async (req, res) => {
  const { resumeId } = req.params;

  try {
    const updated = await ResumeProfile.findByIdAndUpdate(
      resumeId,
      { $set: { summery: "" } }, // or use null if you prefer
      { new: true }
    );

    if (!updated) {
      return res.status(404).json({ error: 'Resume not found' });
    }

    res.status(200).json({ message: 'Summary deleted successfully', updated });
  } catch (err) {
    res.status(500).json({ error: 'Internal server error', details: err.message });
  }
};
exports.deleteProject = (req, res) => {
  deleteByIdFromResume(res, req.params.resumeId, 'Projects', req.params.id, 'Project');
};
