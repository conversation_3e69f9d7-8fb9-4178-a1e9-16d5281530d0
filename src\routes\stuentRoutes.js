const express = require('express');
const router = express.Router();
const authMiddleware = require('../middlewares/authMiddleware');
// const upload = require('../middleware/upload');

const {
    getProfile,
    updateProfile,
    updateSkills,
    updateEducation,
    uploadResume
} = require('../controllers/student/profileController');

const {
    getAllJobs,
    getJobById,
    applyToJob,
    getMyApplications
} = require('../controllers/student/jobController');

const {
    getUpcomingTests,
    getLiveTests,
    getTestHistory,
    getTestDetails,
    startTest,
    submitTest
} = require('../controllers/student/testController');

const {
    getAllResults,
    getResultById
} = require('../controllers/student/resultController');

const {
    getNotifications,
    markAsRead
} = require('../controllers/student/notificationController');

const {
    getJobRecommendations,

} = require('../controllers/student/recommendationController');

// Apply auth middleware to all routes
router.use(authMiddleware);

//  1. RESUME / PROFILE MANAGEMENT

router.get('/profile', getProfile);
router.put('/profile', updateProfile);
router.put('/profile/skills', updateSkills);
router.put('/profile/education', updateEducation);
router.post('/profile/upload-resume', uploadResume);

//  2. JOB LISTINGS & APPLICATIONS

router.get('/jobs', getAllJobs);
router.get('/jobs/:jobId', getJobById);
router.post('/jobs/:jobId/apply', applyToJob);
router.get('/applications', getMyApplications);

//  3. TEST SCHEDULE & PARTICIPATION

router.get('/tests/upcoming', getUpcomingTests);
router.get('/tests/live', getLiveTests);
router.get('/tests/history', getTestHistory);
router.get('/tests/:testId', getTestDetails);
router.post('/tests/:testId/start', startTest);
router.post('/tests/:testId/submit', submitTest);

//  4. TEST RESULTS & FEEDBACK

router.get('/results', getAllResults);
router.get('/results/:testId', getResultById);

//  5. NOTIFICATIONS (OPTIONAL)

// router.get('/notifications', getNotifications);
// router.put('/notifications/:id/read', markAsRead);

//  6. RECOMMENDATIONS (OPTIONAL)

router.get('/recommendations/jobs', getJobRecommendations);
// router.get('/recommendations/tests', getTestRecommendations);

//  7. DASHBOARD & STATS


// router.get('/dashboard', async (req, res) => {
//     try {
//         const studentId = req.user.id;
//         const dashboardData = {
//             upcomingTests: await getUpcomingTestsCount(studentId),
//             activeApplications: await getActiveApplicationsCount(studentId),
//             completedTests: await getCompletedTestsCount(studentId),
//             profileCompletion: await getProfileCompletionStatus(studentId)
//         };
//         res.json({ success: true, data: dashboardData });
//     } catch (error) {
//         res.status(500).json({ success: false, message: 'Error fetching dashboard data', error: error.message });
//     }
// });

// router.get('/stats', async (req, res) => {
//     try {
//         const studentId = req.user.id;
//         const stats = {
//             totalApplications: await getTotalApplications(studentId),
//             testsTaken: await getTotalTestsTaken(studentId),
//             averageScore: await getAverageTestScore(studentId),
//             skillsCount: await getSkillsCount(studentId),
//             shortlistedCount: await getShortlistedCount(studentId)
//         };
//         res.json({ success: true, data: stats });
//     } catch (error) {
//         res.status(500).json({ success: false, message: 'Error fetching statistics', error: error.message });
//     }
// });

//  8. SEARCH & FILTER

// router.get('/search/jobs', async (req, res) => {
//     try {
//         const { q, location, salary, experience, tech, company } = req.query;
//         const searchResults = await searchJobs({ q, location, salary, experience, tech, company, studentId: req.user.id });
//         res.json({ success: true, data: searchResults });
//     } catch (error) {
//         res.status(500).json({ success: false, message: 'Error searching jobs', error: error.message });
//     }
// });

// router.get('/search/tests', async (req, res) => {
//     try {
//         const { domain, difficulty, duration } = req.query;
//         const searchResults = await searchTests({ domain, difficulty, duration, studentId: req.user.id });
//         res.json({ success: true, data: searchResults });
//     } catch (error) {
//         res.status(500).json({ success: false, message: 'Error searching tests', error: error.message });
//     }
// });

//  9. PRACTICE & SKILL DEVELOPMENT

// router.get('/practice/tests', async (req, res) => {
//     try {
//         const practiceTests = await getPracticeTests(req.user.id);
//         res.json({ success: true, data: practiceTests });
//     } catch (error) {
//         res.status(500).json({ success: false, message: 'Error fetching practice tests', error: error.message });
//     }
// });

// router.post('/practice/tests/:testId/start', async (req, res) => {
//     try {
//         const session = await startPracticeTest(req.params.testId, req.user.id);
//         res.json({ success: true, data: session });
//     } catch (error) {
//         res.status(500).json({ success: false, message: 'Error starting practice test', error: error.message });
//     }
// });

//  Helper Functions (For Demo)
// In actual project, move these to /services or /utils


// async function getUpcomingTestsCount(studentId) { return 3; }
// async function getActiveApplicationsCount(studentId) { return 5; }
// async function getCompletedTestsCount(studentId) { return 8; }
// async function getProfileCompletionStatus(studentId) { return 75; }
// async function getTotalApplications(studentId) { return 12; }
// async function getTotalTestsTaken(studentId) { return 8; }
// async function getAverageTestScore(studentId) { return 78.5; }
// async function getSkillsCount(studentId) { return 15; }
// async function getShortlistedCount(studentId) { return 3; }
// async function searchJobs(filters) { return []; }
// async function searchTests(filters) { return []; }
// async function getPracticeTests(studentId) { return []; }
// async function startPracticeTest(testId, studentId) { return {}; }

module.exports = router;
