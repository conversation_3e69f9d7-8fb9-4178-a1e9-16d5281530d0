/**
 * Helper functions for MongoDB aggregation pipelines
 */

const mongoose = require('mongoose');

/**
 * Create optimized aggregation pipeline for candidate data with resume information
 */
const createCandidateAggregationPipeline = (options) => {
    const {
        companyId,
        jobId = null,
        filters = {},
        page = 1,
        limit = 10,
        sortBy = 'appliedAt',
        sortOrder = 'desc',
        includeJobInfo = true
    } = options;

    const pipeline = [];

    // Stage 1: Match jobs for this company
    const matchStage = {
        $match: {
            companyId,
            ...(jobId ? { _id: mongoose.Types.ObjectId.createFromHexString(jobId) } : {})
        }
    };
    pipeline.push(matchStage);

    // Stage 2: Unwind applicants
    pipeline.push({
        $unwind: {
            path: '$applicants',
            preserveNullAndEmptyArrays: false
        }
    });

    // Stage 3: Filter by application status if specified
    if (filters.status && filters.status !== 'all') {
        pipeline.push({
            $match: {
                'applicants.status': filters.status
            }
        });
    }

    // Stage 4: Lookup user data with projection for performance
    pipeline.push({
        $lookup: {
            from: 'users',
            localField: 'applicants.candidateId',
            foreignField: '_id',
            as: 'candidateUser',
            pipeline: [
                {
                    $project: {
                        name: 1,
                        email: 1,
                        role: 1
                    }
                }
            ]
        }
    });

    pipeline.push({
        $unwind: {
            path: '$candidateUser',
            preserveNullAndEmptyArrays: true
        }
    });

    // Stage 5: Lookup resume data
    pipeline.push({
        $lookup: {
            from: 'resumeprofiles',
            localField: 'candidateUser.email',
            foreignField: 'Email',
            as: 'resumeData'
        }
    });

    pipeline.push({
        $unwind: {
            path: '$resumeData',
            preserveNullAndEmptyArrays: true
        }
    });

    // Stage 6: Add resume-based filtering
    const resumeFilters = buildResumeFilters(filters);
    if (Object.keys(resumeFilters).length > 0) {
        pipeline.push({ $match: resumeFilters });
    }

    // Stage 7: Add experience calculation and filtering
    const experienceStages = buildExperienceFilters(filters);
    pipeline.push(...experienceStages);

    // Stage 8: Project final structure
    const projectStage = buildProjectStage(includeJobInfo);
    pipeline.push(projectStage);

    return pipeline;
};

/**
 * Build resume filters for MongoDB aggregation
 */
const buildResumeFilters = (filters) => {
    const {
        skills,
        location,
        education,
        languages,
        certifications,
        searchTerm
    } = filters;

    const matchConditions = {};

    if (skills) {
        const skillsArray = skills.split(',').map(s => s.trim());
        matchConditions['resumeData.Skills.Skill'] = {
            $in: skillsArray.map(skill => new RegExp(skill, 'i'))
        };
    }

    if (location) {
        matchConditions['resumeData.Location'] = new RegExp(location, 'i');
    }

    if (education) {
        matchConditions['resumeData.Education.Degree'] = new RegExp(education, 'i');
    }

    if (languages) {
        const languagesArray = languages.split(',').map(l => l.trim());
        matchConditions['resumeData.Languages.Name'] = {
            $in: languagesArray.map(lang => new RegExp(lang, 'i'))
        };
    }

    if (certifications) {
        matchConditions['resumeData.Certifications.Title'] = new RegExp(certifications, 'i');
    }

    if (searchTerm) {
        matchConditions.$or = [
            { 'candidateUser.name': new RegExp(searchTerm, 'i') },
            { 'candidateUser.email': new RegExp(searchTerm, 'i') },
            { 'resumeData.Title': new RegExp(searchTerm, 'i') },
            { 'resumeData.summery': new RegExp(searchTerm, 'i') },
            { 'resumeData.Headline': new RegExp(searchTerm, 'i') },
            { 'title': new RegExp(searchTerm, 'i') }
        ];
    }

    return matchConditions;
};

/**
 * Build experience filtering stages
 */
const buildExperienceFilters = (filters) => {
    const { experienceLevel, minExperience, maxExperience } = filters;
    
    if (!experienceLevel && !minExperience && !maxExperience) {
        return [];
    }

    const stages = [];

    // Add field to calculate total experience
    stages.push({
        $addFields: {
            totalExperience: {
                $reduce: {
                    input: '$resumeData.Experience',
                    initialValue: 0,
                    in: {
                        $add: [
                            '$$value',
                            {
                                $divide: [
                                    {
                                        $subtract: [
                                            { $ifNull: ['$$this.EndDate', new Date()] },
                                            '$$this.StartDate'
                                        ]
                                    },
                                    365.25 * 24 * 60 * 60 * 1000
                                ]
                            }
                        ]
                    }
                }
            }
        }
    });

    const expConditions = {};
    
    if (minExperience) {
        expConditions.totalExperience = { $gte: parseFloat(minExperience) };
    }
    
    if (maxExperience) {
        expConditions.totalExperience = { 
            ...expConditions.totalExperience,
            $lte: parseFloat(maxExperience) 
        };
    }
    
    if (experienceLevel) {
        const expLevelMap = {
            'entry': { $lt: 2 },
            'mid': { $gte: 2, $lt: 5 },
            'senior': { $gte: 5, $lt: 10 },
            'lead': { $gte: 10 }
        };
        if (expLevelMap[experienceLevel.toLowerCase()]) {
            expConditions.totalExperience = expLevelMap[experienceLevel.toLowerCase()];
        }
    }

    if (Object.keys(expConditions).length > 0) {
        stages.push({ $match: expConditions });
    }

    return stages;
};

/**
 * Build project stage for final output
 */
const buildProjectStage = (includeJobInfo = true) => {
    const baseProject = {
        _id: 0,
        application: {
            candidateId: '$applicants.candidateId',
            appliedAt: '$applicants.appliedAt',
            status: '$applicants.status',
            testScore: '$applicants.testScore'
        },
        candidate: {
            name: '$candidateUser.name',
            email: '$candidateUser.email',
            userId: '$candidateUser._id'
        },
        resume: '$resumeData',
        totalExperience: { $ifNull: ['$totalExperience', 0] }
    };

    if (includeJobInfo) {
        baseProject.jobId = '$_id';
        baseProject.jobTitle = '$title';
        baseProject.jobCategory = '$category';
        baseProject.jobLocation = '$location';
    }

    return { $project: baseProject };
};

/**
 * Add sorting and pagination to pipeline
 */
const addSortingAndPagination = (pipeline, sortBy, sortOrder, page, limit) => {
    // Add sorting
    const sortField = sortBy === 'appliedAt' ? 'application.appliedAt' : 
                     sortBy === 'name' ? 'candidate.name' :
                     sortBy === 'experience' ? 'totalExperience' :
                     sortBy === 'jobTitle' ? 'jobTitle' : 'application.appliedAt';
    
    pipeline.push({
        $sort: { [sortField]: sortOrder === 'desc' ? -1 : 1 }
    });

    // Add pagination
    pipeline.push(
        { $skip: (page - 1) * parseInt(limit) },
        { $limit: parseInt(limit) }
    );

    return pipeline;
};

/**
 * Create count pipeline for pagination
 */
const createCountPipeline = (basePipeline) => {
    return [...basePipeline, { $count: 'total' }];
};

module.exports = {
    createCandidateAggregationPipeline,
    buildResumeFilters,
    buildExperienceFilters,
    buildProjectStage,
    addSortingAndPagination,
    createCountPipeline
};
