const UserProfileDB = require('../models/UserProfileDB');

exports.testAPI = (req, res) => {
  res.send('DeleteInfo API is working');
};

exports.deleteWebsite = async (req, res) => {
  try {
    const user = await UserProfileDB.findOneAndUpdate(
      { _id: req.user.email },
      { Website: "" },
      { new: true }
    );
    if (!user) return res.status(404).json({ error: 'User not found' });
    res.status(200).json({ message: 'Website URL deleted successfully' });
  } catch (err) {
    res.status(500).json({ error: 'Internal server error' });
  }
};

exports.deletePhone = async (req, res) => {
  try {
    await UserProfileDB.findOneAndUpdate(
      { _id: req.user.email },
      { Phone: "" },
      { new: true }
    );
    res.status(200).json({ message: 'Phone deleted' });
  } catch (err) {
    res.status(500).json({ error: 'Internal error' });
  }
};

exports.deleteLocation = async (req, res) => {
  try {
    await UserProfileDB.findOneAndUpdate(
      { _id: req.user.email },
      { Location: "" },
      { new: true }
    );
    res.status(200).json({ message: 'Location deleted' });
  } catch (err) {
    res.status(500).json({ error: 'Internal server error' });
  }
};

const deleteByIdFromArray = async (res, userId, field, subDocId, label) => {
  try {
    const user = await UserProfileDB.findOneAndUpdate(
      { _id: userId },
      { $pull: { [field]: { _id: subDocId } } },
      { new: true }
    );
    if (!user) return res.status(404).json({ error: 'User not found' });
    res.status(200).json({ message: `${label} deleted`, user });
  } catch (err) {
    res.status(500).json({ error: 'Internal server error' });
  }
};

exports.deleteProfile = (req, res) => {
  deleteByIdFromArray(res, req.user.email, 'Profiles', req.params.id, 'Profile');
};

exports.deleteExperience = (req, res) => {
  deleteByIdFromArray(res, req.user.email, 'Experience', req.params.id, 'Experience');
};

exports.deleteEducation = (req, res) => {
  deleteByIdFromArray(res, req.user.email, 'Education', req.params.id, 'Education');
};

exports.deleteSkill = (req, res) => {
  deleteByIdFromArray(res, req.user.email, 'Skills', req.params.id, 'Skill');
};

exports.deleteLanguage = (req, res) => {
  deleteByIdFromArray(res, req.user.email, 'Languages', req.params.id, 'Language');
};

exports.deleteAward = (req, res) => {
  deleteByIdFromArray(res, req.user.email, 'Awards', req.params.id, 'Award');
};

exports.deleteCertification = (req, res) => {
  deleteByIdFromArray(res, req.user.email, 'Certifications', req.params.id, 'Certification');
};

exports.deleteInterest = (req, res) => {
  deleteByIdFromArray(res, req.user.email, 'Interests', req.params.id, 'Interest');
};

exports.deletePublication = (req, res) => {
  deleteByIdFromArray(res, req.user.email, 'Publications', req.params.id, 'Publication');
};

exports.deleteVolunteering = (req, res) => {
  deleteByIdFromArray(res, req.user.email, 'Volunteering', req.params.id, 'Volunteering');
};

exports.deleteReference = (req, res) => {
  deleteByIdFromArray(res, req.user.email, 'References', req.params.id, 'Reference');
};

exports.deleteProject = (req, res) => {
  deleteByIdFromArray(res, req.user.email, 'Project', req.params.id, 'Project');
};


exports.deleteSummary = async (req, res) => {
  const { id } = req.params;

  try {
    const user = await UserProfileDB.findOneAndUpdate(
      { _id: req.user.email },
      { $pull: { Summary: { _id: id } } },
      { new: true }
    );

    if (!user) return res.status(404).json({ error: "User not found" });

    res.status(200).json({
      message: "Summary deleted successfully",
      user,
    });
  } catch (err) {
    console.error("Error deleting summary:", err);
    res.status(500).json({ error: "Internal server error" });
  }
};
