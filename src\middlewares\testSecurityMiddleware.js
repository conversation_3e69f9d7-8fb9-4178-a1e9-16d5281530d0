const Test = require('../models/Test');
const Company = require('../models/Company');
const Job = require('../models/Job');
const mongoose = require('mongoose');

// Middleware to verify test ownership
const verifyTestOwnership = async (req, res, next) => {
    try {
        const { testId } = req.params;

        if (!mongoose.Types.ObjectId.isValid(testId)) {
            return res.status(400).json({ error: 'Invalid test ID' });
        }

        // First, try to find the test
        const test = await Test.findById(testId);
        if (!test) {
            return res.status(404).json({ error: 'Test not found' });
        }

        // Check if user has company role
        if (req.user.role !== 'company' && req.user.role !== 'admin') {
            return res.status(403).json({ error: 'Access denied. Company role required.' });
        }

        // For company users, verify ownership
        if (req.user.role === 'company') {
            // Check if test belongs to this user directly (legacy format)
            if (test.companyId.toString() === req.user.id) {
                // Find company profile for additional info
                const company = await Company.findOne({ userId: req.user.id });
                req.test = test;
                req.company = company;
                return next();
            }

            // Check if test belongs to company profile
            const company = await Company.findOne({ userId: req.user.id });
            if (company && test.companyId.toString() === company._id.toString()) {
                req.test = test;
                req.company = company;
                return next();
            }

            return res.status(403).json({ error: 'Test not found or access denied' });
        }

        // For admin users, allow access
        if (req.user.role === 'admin') {
            req.test = test;
            req.company = null; // Admin doesn't have company
            return next();
        }

        return res.status(403).json({ error: 'Access denied' });
    } catch (error) {
        console.error('Test ownership verification error:', error);
        return res.status(500).json({ error: 'Test ownership verification failed' });
    }
};

// Middleware to verify candidate access to test
const verifyCandidateTestAccess = async (req, res, next) => {
    try {
        const { testId } = req.params;
        const candidateId = req.user.id;

        if (!mongoose.Types.ObjectId.isValid(testId)) {
            return res.status(400).json({ error: 'Invalid test ID' });
        }

        const test = await Test.findOne({
            _id: testId,
            'participants.candidateId': candidateId,
            isActive: true
        });

        if (!test) {
            return res.status(404).json({ 
                error: 'Test not found or you are not assigned to this test' 
            });
        }

        const participant = test.participants.find(
            p => p.candidateId.toString() === candidateId
        );

        if (!participant) {
            return res.status(403).json({ error: 'Access denied to this test' });
        }

        // Check if test is within scheduled time
        const now = new Date();
        if (now < test.scheduledDate || now > test.endDate) {
            return res.status(400).json({ 
                error: 'Test is not available at this time' 
            });
        }

        req.test = test;
        req.participant = participant;
        next();
    } catch (error) {
        return res.status(500).json({ error: 'Test access verification failed' });
    }
};

// Middleware to verify job application before test assignment
const verifyJobApplication = async (req, res, next) => {
    try {
        const { candidateIds } = req.body;
        const { testId } = req.params;

        if (!candidateIds || !Array.isArray(candidateIds)) {
            return res.status(400).json({ error: 'Candidate IDs are required' });
        }

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const test = await Test.findOne({
            _id: testId,
            companyId: company._id
        }).populate('associatedJobs');

        if (!test) {
            return res.status(404).json({ error: 'Test not found' });
        }

        // If test has associated jobs, verify candidates applied for them
        if (test.associatedJobs && test.associatedJobs.length > 0) {
            const jobIds = test.associatedJobs.map(job => job._id);
            
            // Check if all candidates have applied for at least one associated job
            const applicantCheck = await Job.aggregate([
                {
                    $match: {
                        _id: { $in: jobIds },
                        'applicants.candidateId': { 
                            $in: candidateIds.map(id => mongoose.Types.ObjectId.createFromHexString(id)) 
                        }
                    }
                },
                { $unwind: '$applicants' },
                {
                    $match: {
                        'applicants.candidateId': { 
                            $in: candidateIds.map(id => mongoose.Types.ObjectId.createFromHexString(id)) 
                        }
                    }
                },
                {
                    $group: {
                        _id: null,
                        appliedCandidates: { $addToSet: '$applicants.candidateId' }
                    }
                }
            ]);

            const appliedCandidateIds = applicantCheck.length > 0 ? 
                applicantCheck[0].appliedCandidates.map(id => id.toString()) : [];

            const unappliedCandidates = candidateIds.filter(
                id => !appliedCandidateIds.includes(id)
            );

            if (unappliedCandidates.length > 0) {
                return res.status(400).json({
                    error: 'Some candidates have not applied for the required jobs',
                    unappliedCandidates,
                    requiredJobs: test.associatedJobs.map(job => ({
                        id: job._id,
                        title: job.title
                    }))
                });
            }
        }

        req.test = test;
        req.company = company;
        next();
    } catch (error) {
        return res.status(500).json({ error: 'Job application verification failed' });
    }
};

// Rate limiting for test operations
const testOperationRateLimit = (maxOperations = 10, windowMs = 60000) => {
    const operations = new Map();

    return (req, res, next) => {
        const userId = req.user.id;
        const now = Date.now();
        
        if (!operations.has(userId)) {
            operations.set(userId, []);
        }

        const userOperations = operations.get(userId);
        
        // Remove old operations outside the window
        const validOperations = userOperations.filter(
            timestamp => now - timestamp < windowMs
        );

        if (validOperations.length >= maxOperations) {
            return res.status(429).json({
                error: 'Too many test operations. Please try again later.',
                retryAfter: Math.ceil(windowMs / 1000)
            });
        }

        validOperations.push(now);
        operations.set(userId, validOperations);
        next();
    };
};

// Input sanitization for test data
const sanitizeTestInput = (req, res, next) => {
    try {
        // Sanitize common fields
        if (req.body.testName) {
            req.body.testName = req.body.testName.trim().substring(0, 200);
        }
        
        if (req.body.description) {
            req.body.description = req.body.description.trim().substring(0, 500);
        }

        if (req.body.instructions) {
            req.body.instructions = req.body.instructions.trim().substring(0, 1000);
        }

        // Validate numeric fields
        if (req.body.duration) {
            const duration = parseInt(req.body.duration);
            if (isNaN(duration) || duration < 5 || duration > 300) {
                return res.status(400).json({ 
                    error: 'Duration must be between 5 and 300 minutes' 
                });
            }
            req.body.duration = duration;
        }

        if (req.body.passingScore) {
            const passingScore = parseInt(req.body.passingScore);
            if (isNaN(passingScore) || passingScore < 0 || passingScore > 100) {
                return res.status(400).json({ 
                    error: 'Passing score must be between 0 and 100' 
                });
            }
            req.body.passingScore = passingScore;
        }

        next();
    } catch (error) {
        return res.status(500).json({ error: 'Input sanitization failed' });
    }
};

module.exports = {
    verifyTestOwnership,
    verifyCandidateTestAccess,
    verifyJobApplication,
    testOperationRateLimit,
    sanitizeTestInput
};
