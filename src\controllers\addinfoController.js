const UserProfileDB = require('../models/UserProfileDB');
const cloudinary = require("../config/cloudinary");

exports.testAPI = (req, res) => {
  res.send('AddInfo API is working');
};

exports.updateWebsite = async (req, res) => {
  const { website } = req.body;
  if (!website) return res.status(400).json({ error: 'Website URL is required' });

  try {
    const user = await UserProfileDB.findOneAndUpdate(
      { _id: req.user.email },
      { Website: website },
      { new: true, upsert: true }
    );
    if (!user) return res.status(404).json({ error: 'User not found' });
    res.status(200).json({ message: 'Website URL updated successfully' });
  } catch (err) {
    res.status(500).json({ error: 'Internal server error' });
  }
};

exports.updatePhone = async (req, res) => {
  const { phone } = req.body;
  if (!phone) return res.status(400).json({ error: 'Phone is required' });

  try {
    const user = await UserProfileDB.findOneAndUpdate(
      { _id: req.user.email },
      { Phone: phone },
      { new: true, upsert: true }
    );
    res.status(200).json({ message: 'Phone updated', user });
  } catch (err) {
    res.status(500).json({ error: 'Internal error' });
  }
};

exports.updateLocation = async (req, res) => {
  const { location } = req.body;
  if (!location) return res.status(400).json({ error: 'Location is required' });

  try {
    const user = await UserProfileDB.findOneAndUpdate(
      { _id: req.user.email },
      { Location: location },
      { new: true, upsert: true }
    );
    res.status(200).json({ message: 'Location updated successfully', user });
  } catch (err) {
    res.status(500).json({ error: 'Internal server error' });
  }
};

exports.addProfile = async (req, res) => {
  const { Network, Username, ProfileLink, ProfileImage } = req.body;
  if (!Network || !Username || !ProfileLink || !ProfileImage)
    return res.status(400).json({ error: 'All fields required' });

  try {
    const user = await UserProfileDB.findOneAndUpdate(
      { _id: req.user.email },
      { $push: { Profiles: { Network, Username, ProfileLink, ProfileImage } } },
      { new: true }
    );
    res.status(200).json({ message: 'Profile added', user });
  } catch (err) {
    res.status(500).json({ error: 'Internal error' });
  }
};

exports.addExperience = async (req, res) => {
  const data = req.body;
  if (!data.Company || !data.Position || !data.StartDate)
    return res.status(400).json({ error: 'Company, Position & StartDate required' });

  try {
    const user = await UserProfileDB.findOneAndUpdate(
      { _id: req.user.email },
      { $push: { Experience: data } },
      { new: true }
    );
    res.status(200).json({ message: 'Experience added', user });
  } catch (err) {
    res.status(500).json({ error: 'Internal error' });
  }
};

exports.addEducation = async (req, res) => {
  const data = req.body;
  if (!data.Institution || !data.Degree || !data.StartDate)
    return res.status(400).json({ error: 'Required fields missing' });

  try {
    const user = await UserProfileDB.findOneAndUpdate(
      { _id: req.user.email },
      { $push: { Education: data } },
      { new: true }
    );
    res.status(200).json({ message: 'Education added', user });
  } catch (err) {
    res.status(500).json({ error: 'Internal error' });
  }
};
exports.addSkill = async (req, res) => {
  const { skill, proficiency } = req.body;

  if (!skill || !proficiency)
    return res.status(400).json({ error: 'Skill and Proficiency are required' });

  try {
    const user = await UserProfileDB.findOneAndUpdate(
      { _id: req.user.email },
      { $push: { Skills: req.body } },
      { new: true }
    );
    res.status(200).json({ message: 'Skill added', user });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Internal error' });
  }
};

exports.addLanguage = async (req, res) => {
  const { Name, Proficiency } = req.body;
  if (!Name || !Proficiency)
    return res.status(400).json({ error: 'Name and Proficiency are required' });

  try {
    const user = await UserProfileDB.findOneAndUpdate(
      { _id: req.user.email },
      { $push: { Languages: req.body } },
      { new: true }
    );
    res.status(200).json({ message: 'Language added', user });
  } catch (err) {
    res.status(500).json({ error: 'Internal server error' });
  }
};

exports.addAward = async (req, res) => {
  const { Title, Issuer, Date } = req.body;
  if (!Title || !Issuer || !Date)
    return res.status(400).json({ error: 'Title, Issuer, and Date are required' });

  try {
    const user = await UserProfileDB.findOneAndUpdate(
      { _id: req.user.email },
      { $push: { Awards: req.body } },
      { new: true }
    );
    res.status(200).json({ message: 'Award added', user });
  } catch (err) {
    res.status(500).json({ error: 'Internal server error' });
  }
};

exports.addCertification = async (req, res) => {
  const { Title, Issuer, Date } = req.body;
  if (!Title || !Issuer || !Date)
    return res.status(400).json({ error: 'Title, Issuer, and Date are required' });

  try {
    const user = await UserProfileDB.findOneAndUpdate(
      { _id: req.user.email },
      { $push: { Certifications: req.body } },
      { new: true }
    );
    res.status(200).json({ message: 'Certification added', user });
  } catch (err) {
    res.status(500).json({ error: 'Internal server error' });
  }
};

exports.addInterest = async (req, res) => {
  const { Interest } = req.body;
  if (!Interest) return res.status(400).json({ error: 'Interest is required' });

  try {
    const user = await UserProfileDB.findOneAndUpdate(
      { _id: req.user.email },
      { $push: { Interests: req.body } },
      { new: true }
    );
    res.status(200).json({ message: 'Interest added', user });
  } catch (err) {
    res.status(500).json({ error: 'Internal server error' });
  }
};

exports.addPublication = async (req, res) => {
  const { Title, Publisher, Date } = req.body;
  if (!Title || !Publisher || !Date)
    return res.status(400).json({ error: 'Title, Publisher, and Date are required' });

  try {
    const user = await UserProfileDB.findOneAndUpdate(
      { _id: req.user.email },
      { $push: { Publications: req.body } },
      { new: true }
    );
    res.status(200).json({ message: 'Publication added', user });
  } catch (err) {
    res.status(500).json({ error: 'Internal server error' });
  }
};

exports.addVolunteering = async (req, res) => {
  const { Organization, Position, StartDate } = req.body;
  if (!Organization || !Position || !StartDate)
    return res.status(400).json({ error: 'Required fields missing' });

  try {
    const user = await UserProfileDB.findOneAndUpdate(
      { _id: req.user.email },
      { $push: { Volunteering: req.body } },
      { new: true }
    );
    res.status(200).json({ message: 'Volunteering added', user });
  } catch (err) {
    res.status(500).json({ error: 'Internal server error' });
  }
};

exports.addReference = async (req, res) => {
  const { Name, Position, Company, Email } = req.body;
  if (!Name || !Position || !Company || !Email)
    return res.status(400).json({ error: 'All fields are required' });

  try {
    const user = await UserProfileDB.findOneAndUpdate(
      { _id: req.user.email },
      { $push: { References: req.body } },
      { new: true }
    );
    res.status(200).json({ message: 'Reference added', user });
  } catch (err) {
    res.status(500).json({ error: 'Internal server error' });
  }
};

exports.uploadImage = async (req, res) => {
  try {
    if (!req.file || !req.file.path) {
      return res.status(400).json({ error: "No image file uploaded" });
    }

    //  Get the secure URL and public_id from cloudinary response
    const { path: secure_url, filename: public_id } = req.file;

    const user = await UserProfileDB.findOneAndUpdate(
      { _id: req.user.email },
      {
        ProfilePic: secure_url,
        ProfilePicPublicId: public_id, // * Save public ID for deletion
      },
      { new: true, upsert: true }
    );

    if (!user) return res.status(404).json({ error: "User not found" });

    res.status(200).json({
      message: "Image uploaded successfully",
      url: secure_url,
      public_id,
    });
  } catch (err) {
    console.error("Cloudinary upload error:", err);
    res.status(500).json({ error: "Internal server error" });
  }
};
exports.deleteImage = async (req, res) => {
  try {
    const user = await UserProfileDB.findOne({ _id: req.user.email });

    if (!user || !user.ProfilePicPublicId) {
      return res.status(404).json({ error: "Profile picture not found" });
    }

    // Delete from cloudinary
    await cloudinary.uploader.destroy(user.ProfilePicPublicId);

    // Clear image data
    user.ProfilePic = "";
    user.ProfilePicPublicId = "";

    // Skip unrelated schema validation
    await user.save({ validateBeforeSave: false });

    res.status(200).json({ message: "Profile picture deleted successfully" });
  } catch (err) {
    console.error("Error deleting image:", err);
    res.status(500).json({ error: "Failed to delete profile picture" });
  }
};
exports.addProject = async (req, res) => {
  const { Name, Description } = req.body;

  // Validate required fields
  if (!Name || !Description) {
    return res.status(400).json({ error: 'Name and Description are required' });
  }

  try {
    // Find user and push project into Project array
    const user = await UserProfileDB.findOneAndUpdate(
      { _id: req.user.email },
      { $push: { Project: req.body } },
      { new: true }
    );

    res.status(200).json({
      message: 'Project added successfully',
      project: req.body,
      user
    });
  } catch (err) {
    console.error('Error adding project:', err);
    res.status(500).json({ error: 'Internal server error' });
  }
};

exports.addSummary = async (req, res) => {
  const { text } = req.body;

  if (!text) {
    return res.status(400).json({ error: "Summary text is required" });
  }

  try {
    const user = await UserProfileDB.findOneAndUpdate(
      { _id: req.user.email },
      { $push: { Summary: { text } } },
      { new: true }
    );

    res.status(200).json({
      message: "Summary added successfully",
      summary: { text },
      user,
    });
  } catch (err) {
    console.error("Error adding summary:", err);
    res.status(500).json({ error: "Internal server error" });
  }
};