// routes/companyRoutes.js
const express = require('express');
const router = express.Router();
const authMiddleware = require('../middlewares/authMiddleware');
const roleMiddleware = require('../middlewares/roleMiddleware');
const companyController = require('../controllers/companyController');

router.use(authMiddleware);
router.use(roleMiddleware(['interviewer', 'company'])); // Apply role restriction


// Company Profile Management
router.post('/profile', companyController.createCompanyProfile);
router.get('/profile', companyController.getCompanyProfile);
router.put('/profile', companyController.updateCompanyProfile);
// Not implemented in controller, hence commented out
// router.delete('/profile', companyController.deleteCompanyProfile);

// Job Management
router.post('/jobs', companyController.createJob);
router.get('/jobs', companyController.getCompanyJobs);
router.get('/jobs/:jobId', companyController.getJobDetails);
router.put('/jobs/:jobId', companyController.updateJob);
router.delete('/jobs/:jobId', companyController.deleteJob);

// Optional (if job status updates are required)
// Not implemented yet:
router.patch('/jobs/:jobId/status', companyController.updateJobStatus);

router.put('/jobs/bulk-update', companyController.bulkUpdateJobs); // Bulk update route

// Application Management (Basic)
router.get('/jobs/:jobId/applications', companyController.getJobApplications);
router.put('/jobs/:jobId/applications/:candidateId', companyController.updateApplicationStatus);

// Enhanced Application Management with Resume Data
router.get('/jobs/:jobId/applications-with-resumes', companyController.getJobApplicationsWithResumes);
router.get('/applications-with-resumes', companyController.getAllApplicationsWithResumes);
router.get('/jobs/:jobId/candidates/:candidateId', companyController.getCandidateDetails);
router.get('/candidates/analytics/:jobId?', companyController.getCandidateAnalytics);

// Interview Management
// Not implemented yet:
// router.post('/jobs/:jobId/applications/:candidateId/interview', companyController.scheduleInterview);
// router.put('/interviews/:interviewId', companyController.updateInterview);
// router.get('/interviews', companyController.getCompanyInterviews);
// router.delete('/interviews/:interviewId', companyController.cancelInterview);

// Analytics & Dashboard
router.get('/dashboard', companyController.getCompanyDashboard);
router.get('/jobs/:jobId/analytics', companyController.getJobAnalytics);

// Company Settings
// Not implemented yet:
// router.get('/settings', companyController.getCompanySettings);
// router.put('/settings', companyController.updateCompanySettings);

module.exports = router;
