
// controllers/student/resultController.js
const Result = require('../../models/Result');
const Test = require('../../models/Test');
const Job = require('../../models/Job');
const User = require('../../models/User');

// GET ALL RESULTS FOR A CANDIDATE
const getAllResults = async (req, res) => {
    try {
        const candidateId = req.user.id;
        const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;

        // Build query
        const query = { candidateId };

        // Calculate pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);

        // Build sort object
        const sort = {};
        sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

        // Get results with populated data
        const results = await Result.find(query)
            .populate({
                path: 'testId',
                select: 'title description duration totalMarks domain',
                populate: {
                    path: 'companyId',
                    select: 'companyName logo'
                }
            })
            .populate({
                path: 'jobId',
                select: 'title company location jobType'
            })
            .sort(sort)
            .skip(skip)
            .limit(parseInt(limit))
            .lean();

        // Get total count for pagination
        const totalResults = await Result.countDocuments(query);
        const totalPages = Math.ceil(totalResults / parseInt(limit));

        // Format results for response
        const formattedResults = results.map(result => ({
            resultId: result._id,
            test: {
                id: result.testId._id,
                title: result.testId.title,
                description: result.testId.description,
                duration: result.testId.duration,
                totalMarks: result.testId.totalMarks,
                domain: result.testId.domain,
                company: result.testId.companyId ? {
                    name: result.testId.companyId.companyName,
                    logo: result.testId.companyId.logo
                } : null
            },
            job: result.jobId ? {
                id: result.jobId._id,
                title: result.jobId.title,
                company: result.jobId.company,
                location: result.jobId.location,
                jobType: result.jobId.jobType
            } : null,
            performance: {
                score: result.score,
                totalScore: result.totalScore,
                percentage: result.percentage || Math.round((result.score / result.totalScore) * 100)
            },
            feedback: result.feedback,
            testDate: result.createdAt,
            status: result.percentage >= 60 ? 'Passed' : 'Failed', // Configurable passing threshold
            tabSwitchCount: result.tabSwitchCount,
            suspiciousActivity: result.suspiciousActivity
        }));

        // Calculate statistics
        const stats = {
            totalTests: totalResults,
            averageScore: results.length > 0 ?
                Math.round(results.reduce((sum, r) => sum + (r.percentage || 0), 0) / results.length) : 0,
            testsPassed: results.filter(r => (r.percentage || 0) >= 60).length,
            testsFailed: results.filter(r => (r.percentage || 0) < 60).length
        };

        res.status(200).json({
            success: true,
            message: 'Results retrieved successfully',
            data: {
                results: formattedResults,
                pagination: {
                    currentPage: parseInt(page),
                    totalPages,
                    totalResults,
                    hasNextPage: parseInt(page) < totalPages,
                    hasPrevPage: parseInt(page) > 1
                },
                statistics: stats
            }
        });

    } catch (error) {
        console.error('Error fetching results:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching test results',
            error: error.message
        });
    }
};

// GET DETAILED RESULT BY TEST ID
const getResultById = async (req, res) => {
    try {
        const candidateId = req.user.id;
        const { testId } = req.params;

        // Find the result for this candidate and test
        const result = await Result.findOne({
            candidateId,
            testId
        })
            .populate({
                path: 'testId',
                select: 'title description duration totalMarks domain questions',
                populate: [
                    {
                        path: 'companyId',
                        select: 'companyName logo website'
                    },
                    {
                        path: 'questions',
                        select: 'questionText options correctAnswer explanation points type'
                    }
                ]
            })
            .populate({
                path: 'jobId',
                select: 'title description company location jobType skillsRequired'
            })
            .populate({
                path: 'evaluatedBy',
                select: 'firstName lastName email'
            });

        if (!result) {
            return res.status(404).json({
                success: false,
                message: 'Test result not found'
            });
        }

        // Format detailed answers with question details
        const detailedAnswers = result.answers.map(answer => {
            const question = result.testId.questions.find(q =>
                q._id.toString() === answer.questionId.toString()
            );

            return {
                questionId: answer.questionId,
                questionText: question?.questionText || 'Question not found',
                questionType: question?.type || 'mcq',
                options: question?.options || [],
                correctAnswer: question?.correctAnswer,
                explanation: question?.explanation,
                maxPoints: question?.points || 1,
                candidateAnswer: answer.answer,
                isCorrect: answer.isCorrect,
                pointsEarned: answer.pointsEarned || 0
            };
        });

        // Calculate performance metrics
        const correctAnswers = result.answers.filter(a => a.isCorrect).length;
        const totalQuestions = result.answers.length;
        const accuracy = totalQuestions > 0 ? Math.round((correctAnswers / totalQuestions) * 100) : 0;

        // Format response
        const detailedResult = {
            resultId: result._id,
            test: {
                id: result.testId._id,
                title: result.testId.title,
                description: result.testId.description,
                duration: result.testId.duration,
                totalMarks: result.testId.totalMarks,
                domain: result.testId.domain,
                company: result.testId.companyId ? {
                    name: result.testId.companyId.companyName,
                    logo: result.testId.companyId.logo,
                    website: result.testId.companyId.website
                } : null
            },
            job: result.jobId ? {
                id: result.jobId._id,
                title: result.jobId.title,
                description: result.jobId.description,
                company: result.jobId.company,
                location: result.jobId.location,
                jobType: result.jobId.jobType,
                skillsRequired: result.jobId.skillsRequired
            } : null,
            performance: {
                score: result.score,
                totalScore: result.totalScore,
                percentage: result.percentage || Math.round((result.score / result.totalScore) * 100),
                correctAnswers,
                totalQuestions,
                accuracy,
                status: (result.percentage || 0) >= 60 ? 'Passed' : 'Failed'
            },
            answers: detailedAnswers,
            feedback: result.feedback,
            evaluatedBy: result.evaluatedBy ? {
                name: `${result.evaluatedBy.firstName} ${result.evaluatedBy.lastName}`,
                email: result.evaluatedBy.email
            } : null,
            testDate: result.createdAt,
            evaluationDate: result.updatedAt,
            proctoring: {
                tabSwitchCount: result.tabSwitchCount,
                suspiciousActivity: result.suspiciousActivity
            }
        };

        res.status(200).json({
            success: true,
            message: 'Detailed result retrieved successfully',
            data: detailedResult
        });

    } catch (error) {
        console.error('Error fetching detailed result:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching detailed test result',
            error: error.message
        });
    }
};

// GET PERFORMANCE ANALYTICS
const getPerformanceAnalytics = async (req, res) => {
    try {
        const candidateId = req.user.id;
        const { timeframe = '6months' } = req.query;

        // Calculate date range
        const now = new Date();
        let startDate;

        switch (timeframe) {
            case '1month':
                startDate = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
                break;
            case '3months':
                startDate = new Date(now.getFullYear(), now.getMonth() - 3, now.getDate());
                break;
            case '1year':
                startDate = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
                break;
            default: // 6months
                startDate = new Date(now.getFullYear(), now.getMonth() - 6, now.getDate());
        }

        // Get results within timeframe
        const results = await Result.find({
            candidateId,
            createdAt: { $gte: startDate }
        })
            .populate('testId', 'domain title')
            .sort({ createdAt: 1 });

        // Calculate overall statistics
        const totalTests = results.length;
        const averageScore = totalTests > 0 ?
            Math.round(results.reduce((sum, r) => sum + (r.percentage || 0), 0) / totalTests) : 0;

        const testsPassed = results.filter(r => (r.percentage || 0) >= 60).length;
        const passRate = totalTests > 0 ? Math.round((testsPassed / totalTests) * 100) : 0;

        // Performance by domain
        const domainPerformance = {};
        results.forEach(result => {
            const domain = result.testId.domain || 'General';
            if (!domainPerformance[domain]) {
                domainPerformance[domain] = {
                    domain,
                    totalTests: 0,
                    averageScore: 0,
                    bestScore: 0,
                    testsPassed: 0
                };
            }

            domainPerformance[domain].totalTests++;
            domainPerformance[domain].averageScore += result.percentage || 0;
            domainPerformance[domain].bestScore = Math.max(
                domainPerformance[domain].bestScore,
                result.percentage || 0
            );
            if ((result.percentage || 0) >= 60) {
                domainPerformance[domain].testsPassed++;
            }
        });

        // Calculate averages for domains
        Object.keys(domainPerformance).forEach(domain => {
            const data = domainPerformance[domain];
            data.averageScore = Math.round(data.averageScore / data.totalTests);
            data.passRate = Math.round((data.testsPassed / data.totalTests) * 100);
        });

        // Performance trend (monthly)
        const monthlyPerformance = {};
        results.forEach(result => {
            const month = result.createdAt.toISOString().slice(0, 7); // YYYY-MM format
            if (!monthlyPerformance[month]) {
                monthlyPerformance[month] = {
                    month,
                    totalTests: 0,
                    averageScore: 0,
                    totalScore: 0
                };
            }

            monthlyPerformance[month].totalTests++;
            monthlyPerformance[month].totalScore += result.percentage || 0;
        });

        // Calculate monthly averages
        const performanceTrend = Object.keys(monthlyPerformance)
            .map(month => {
                const data = monthlyPerformance[month];
                return {
                    month,
                    totalTests: data.totalTests,
                    averageScore: Math.round(data.totalScore / data.totalTests)
                };
            })
            .sort((a, b) => a.month.localeCompare(b.month));

        // Recent activity (last 5 tests)
        const recentActivity = results.slice(-5).map(result => ({
            testId: result.testId._id,
            testTitle: result.testId.title,
            domain: result.testId.domain,
            score: result.percentage || 0,
            date: result.createdAt,
            status: (result.percentage || 0) >= 60 ? 'Passed' : 'Failed'
        }));

        res.status(200).json({
            success: true,
            message: 'Performance analytics retrieved successfully',
            data: {
                overview: {
                    totalTests,
                    averageScore,
                    testsPassed,
                    passRate,
                    timeframe
                },
                domainPerformance: Object.values(domainPerformance),
                performanceTrend,
                recentActivity
            }
        });

    } catch (error) {
        console.error('Error fetching performance analytics:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching performance analytics',
            error: error.message
        });
    }
};

module.exports = {
    getAllResults,
    getResultById,
    getPerformanceAnalytics
};