const express = require('express');
const updateinfoRoutes = express.Router();
const updateinfoController = require('../controllers/updateinfoController');
const authMiddleware = require('../middlewares/authMiddleware');

updateinfoRoutes.get('/', updateinfoController.testAPI);
updateinfoRoutes.put('/website', authMiddleware, updateinfoController.updateWebsite);
updateinfoRoutes.put('/phone', authMiddleware, updateinfoController.updatePhone);
updateinfoRoutes.put('/location', authMiddleware, updateinfoController.updateLocation);
updateinfoRoutes.put('/profiles', authMiddleware, updateinfoController.updateProfile);
updateinfoRoutes.put('/experience', authMiddleware, updateinfoController.updateExperience);
updateinfoRoutes.put('/education', authMiddleware, updateinfoController.updateEducation);
updateinfoRoutes.put('/skills', authMiddleware, updateinfoController.updateSkill);
updateinfoRoutes.put('/languages', authMiddleware, updateinfoController.updateLanguage);
updateinfoRoutes.put('/awards', authMiddleware, updateinfoController.updateAward);
updateinfoRoutes.put('/certifications', authMiddleware, updateinfoController.updateCertification);
updateinfoRoutes.put('/interests', authMiddleware, updateinfoController.updateInterest);
updateinfoRoutes.put('/publications', authMiddleware, updateinfoController.updatePublication);
updateinfoRoutes.put('/volunteering', authMiddleware, updateinfoController.updateVolunteering);
updateinfoRoutes.put('/references', authMiddleware, updateinfoController.updateReference);

module.exports = updateinfoRoutes;
