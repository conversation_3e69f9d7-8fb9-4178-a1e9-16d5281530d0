# Complete Test API Frontend Integration Guide

## Base Configuration

### API Base URL
```javascript
const API_BASE_URL = 'http://localhost:3000/api/tests';

// Axios configuration with authentication
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true, // For cookie-based auth
});

// Add auth token to requests
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('authToken');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});
```

## 1. CORE TEST MANAGEMENT

### 1.1 Create Test
```javascript
const createTest = async (testData) => {
  try {
    const response = await apiClient.post('/', {
      testName: testData.testName,
      description: testData.description,
      duration: parseInt(testData.duration),
      passingScore: parseInt(testData.passingScore),
      scheduledDate: new Date(testData.scheduledDate).toISOString(),
      endDate: new Date(testData.endDate).toISOString(),
      questions: testData.questions.map(q => ({
        questionId: q.questionId,
        points: q.points || 1
      })),
      associatedJobs: testData.associatedJobs || [],
      instructions: testData.instructions || '',
      allowedAttempts: testData.allowedAttempts || 1,
      randomizeQuestions: testData.randomizeQuestions || false,
      showResults: testData.showResults || false
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Usage Example
const handleCreateTest = async () => {
  const testData = {
    testName: "Frontend Developer Assessment",
    description: "Comprehensive frontend skills test",
    duration: 60,
    passingScore: 70,
    scheduledDate: "2024-01-15T09:00:00Z",
    endDate: "2024-01-15T18:00:00Z",
    questions: [
      { questionId: "questionId1", points: 2 },
      { questionId: "questionId2", points: 3 }
    ],
    associatedJobs: ["jobId1", "jobId2"],
    instructions: "Please read all questions carefully",
    allowedAttempts: 1,
    randomizeQuestions: true,
    showResults: false
  };

  try {
    const result = await createTest(testData);
    console.log('Test created:', result);
  } catch (error) {
    console.error('Error creating test:', error);
  }
};
```

### 1.2 Get All Tests
```javascript
const getAllTests = async () => {
  try {
    const response = await apiClient.get('/');
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Usage Example
const TestsList = () => {
  const [tests, setTests] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTests = async () => {
      try {
        const data = await getAllTests();
        setTests(data.tests);
      } catch (error) {
        console.error('Error fetching tests:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTests();
  }, []);

  if (loading) return <div>Loading tests...</div>;

  return (
    <div>
      <h2>Tests ({tests.length})</h2>
      {tests.map(test => (
        <div key={test._id} className="test-card">
          <h3>{test.testName}</h3>
          <p>{test.description}</p>
          <p>Duration: {test.duration} minutes</p>
          <p>Questions: {test.questions.length}</p>
          <p>Participants: {test.participants.length}</p>
        </div>
      ))}
    </div>
  );
};
```

### 1.3 Get Test Details
```javascript
const getTestDetails = async (testId) => {
  try {
    const response = await apiClient.get(`/${testId}`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Usage Example
const TestDetails = ({ testId }) => {
  const [test, setTest] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTestDetails = async () => {
      try {
        const data = await getTestDetails(testId);
        setTest(data.test);
      } catch (error) {
        console.error('Error fetching test details:', error);
      } finally {
        setLoading(false);
      }
    };

    if (testId) {
      fetchTestDetails();
    }
  }, [testId]);

  if (loading) return <div>Loading test details...</div>;
  if (!test) return <div>Test not found</div>;

  return (
    <div className="test-details">
      <h2>{test.testName}</h2>
      <div className="test-info">
        <p><strong>Description:</strong> {test.description}</p>
        <p><strong>Duration:</strong> {test.duration} minutes</p>
        <p><strong>Passing Score:</strong> {test.passingScore}%</p>
        <p><strong>Scheduled:</strong> {new Date(test.scheduledDate).toLocaleString()}</p>
        <p><strong>End Date:</strong> {new Date(test.endDate).toLocaleString()}</p>
        <p><strong>Total Points:</strong> {test.totalPoints}</p>
      </div>
      
      <div className="questions-section">
        <h3>Questions ({test.questions.length})</h3>
        {test.questions.map((q, index) => (
          <div key={q.questionId._id} className="question-item">
            <p><strong>Q{index + 1}:</strong> {q.questionId.questionText}</p>
            <p>Points: {q.points}</p>
            <p>Type: {q.questionId.questionType}</p>
          </div>
        ))}
      </div>

      <div className="participants-section">
        <h3>Participants ({test.participants.length})</h3>
        {test.participants.map(participant => (
          <div key={participant.candidateId} className="participant-item">
            <p>Status: {participant.status}</p>
            {participant.score && <p>Score: {participant.score}/{participant.totalScore}</p>}
            {participant.percentage && <p>Percentage: {participant.percentage}%</p>}
          </div>
        ))}
      </div>
    </div>
  );
};
```

### 1.4 Update Test
```javascript
const updateTest = async (testId, updateData) => {
  try {
    const response = await apiClient.put(`/${testId}`, updateData);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Usage Example
const EditTestForm = ({ testId, initialData, onUpdate }) => {
  const [formData, setFormData] = useState(initialData);
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const result = await updateTest(testId, formData);
      onUpdate(result.test);
      alert('Test updated successfully!');
    } catch (error) {
      console.error('Error updating test:', error);
      alert('Error updating test: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <div>
        <label>Test Name:</label>
        <input
          type="text"
          value={formData.testName}
          onChange={(e) => setFormData({...formData, testName: e.target.value})}
          required
        />
      </div>
      
      <div>
        <label>Description:</label>
        <textarea
          value={formData.description}
          onChange={(e) => setFormData({...formData, description: e.target.value})}
        />
      </div>
      
      <div>
        <label>Duration (minutes):</label>
        <input
          type="number"
          min="5"
          max="300"
          value={formData.duration}
          onChange={(e) => setFormData({...formData, duration: parseInt(e.target.value)})}
          required
        />
      </div>
      
      <div>
        <label>Passing Score (%):</label>
        <input
          type="number"
          min="0"
          max="100"
          value={formData.passingScore}
          onChange={(e) => setFormData({...formData, passingScore: parseInt(e.target.value)})}
          required
        />
      </div>

      <button type="submit" disabled={loading}>
        {loading ? 'Updating...' : 'Update Test'}
      </button>
    </form>
  );
};
```

### 1.5 Delete Test
```javascript
const deleteTest = async (testId) => {
  try {
    const response = await apiClient.delete(`/${testId}`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Usage Example
const DeleteTestButton = ({ testId, testName, onDelete }) => {
  const [loading, setLoading] = useState(false);

  const handleDelete = async () => {
    if (!window.confirm(`Are you sure you want to delete "${testName}"? This action cannot be undone.`)) {
      return;
    }

    setLoading(true);
    try {
      await deleteTest(testId);
      onDelete(testId);
      alert('Test deleted successfully!');
    } catch (error) {
      console.error('Error deleting test:', error);
      alert('Error deleting test: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <button 
      onClick={handleDelete} 
      disabled={loading}
      className="delete-button"
    >
      {loading ? 'Deleting...' : 'Delete Test'}
    </button>
  );
};
```

## 2. QUESTION MANAGEMENT

### 2.1 Get Questions by Category
```javascript
const getQuestionsByCategory = async (category) => {
  try {
    const response = await apiClient.get(`/questions/by-category?category=${category}`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Usage Example
const QuestionsByCategory = () => {
  const [selectedCategory, setSelectedCategory] = useState('Frontend');
  const [questions, setQuestions] = useState([]);
  const [loading, setLoading] = useState(false);

  const fetchQuestions = async (category) => {
    setLoading(true);
    try {
      const data = await getQuestionsByCategory(category);
      setQuestions(data.questions);
    } catch (error) {
      console.error('Error fetching questions:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchQuestions(selectedCategory);
  }, [selectedCategory]);

  return (
    <div>
      <div>
        <label>Select Category:</label>
        <select 
          value={selectedCategory} 
          onChange={(e) => setSelectedCategory(e.target.value)}
        >
          <option value="Frontend">Frontend</option>
          <option value="Backend">Backend</option>
          <option value="Full Stack">Full Stack</option>
          <option value="Data Science">Data Science</option>
          <option value="DevOps">DevOps</option>
          <option value="Mobile">Mobile</option>
          <option value="UI/UX">UI/UX</option>
          <option value="QA">QA</option>
          <option value="Aptitude">Aptitude</option>
          <option value="Logical">Logical</option>
          <option value="Other">Other</option>
        </select>
      </div>

      {loading ? (
        <div>Loading questions...</div>
      ) : (
        <div>
          <h3>{selectedCategory} Questions ({questions.length})</h3>
          {questions.map(question => (
            <div key={question._id} className="question-card">
              <p><strong>Q:</strong> {question.questionText}</p>
              <p><strong>Type:</strong> {question.questionType}</p>
              <p><strong>Difficulty:</strong> {question.difficulty}</p>
              <p><strong>Points:</strong> {question.points}</p>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
```

### 2.2 Get Question Categories
```javascript
const getQuestionCategories = async () => {
  try {
    const response = await apiClient.get('/questions/categories');
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Usage Example
const CategorySelector = ({ onCategorySelect }) => {
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const data = await getQuestionCategories();
        setCategories(data.categories);
      } catch (error) {
        console.error('Error fetching categories:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  if (loading) return <div>Loading categories...</div>;

  return (
    <div className="category-selector">
      <h3>Question Categories</h3>
      <div className="category-grid">
        {categories.map(cat => (
          <div
            key={cat.category}
            className="category-card"
            onClick={() => onCategorySelect(cat.category)}
          >
            <h4>{cat.category}</h4>
            <p>{cat.count} questions</p>
          </div>
        ))}
      </div>
    </div>
  );
};
```

### 2.3 Filter Questions
```javascript
const filterQuestions = async (filters) => {
  try {
    const params = new URLSearchParams();

    if (filters.searchTerm) params.append('searchTerm', filters.searchTerm);
    if (filters.category) params.append('category', filters.category);
    if (filters.difficulty) params.append('difficulty', filters.difficulty);
    if (filters.type) params.append('type', filters.type);
    if (filters.page) params.append('page', filters.page);
    if (filters.limit) params.append('limit', filters.limit);

    const response = await apiClient.get(`/questions/filter?${params.toString()}`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Usage Example
const QuestionFilter = () => {
  const [filters, setFilters] = useState({
    searchTerm: '',
    category: '',
    difficulty: '',
    type: '',
    page: 1,
    limit: 20
  });
  const [questions, setQuestions] = useState([]);
  const [pagination, setPagination] = useState({});
  const [loading, setLoading] = useState(false);

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1 // Reset to first page when filters change
    }));
  };

  const searchQuestions = async () => {
    setLoading(true);
    try {
      const data = await filterQuestions(filters);
      setQuestions(data.questions);
      setPagination(data.pagination);
    } catch (error) {
      console.error('Error filtering questions:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    searchQuestions();
  }, [filters]);

  return (
    <div className="question-filter">
      <div className="filter-controls">
        <input
          type="text"
          placeholder="Search questions..."
          value={filters.searchTerm}
          onChange={(e) => handleFilterChange('searchTerm', e.target.value)}
        />

        <select
          value={filters.category}
          onChange={(e) => handleFilterChange('category', e.target.value)}
        >
          <option value="">All Categories</option>
          <option value="Frontend">Frontend</option>
          <option value="Backend">Backend</option>
          <option value="Full Stack">Full Stack</option>
          {/* Add other categories */}
        </select>

        <select
          value={filters.difficulty}
          onChange={(e) => handleFilterChange('difficulty', e.target.value)}
        >
          <option value="">All Difficulties</option>
          <option value="Easy">Easy</option>
          <option value="Medium">Medium</option>
          <option value="Hard">Hard</option>
        </select>

        <select
          value={filters.type}
          onChange={(e) => handleFilterChange('type', e.target.value)}
        >
          <option value="">All Types</option>
          <option value="MCQ">MCQ</option>
          <option value="Multiple-Select">Multiple Select</option>
          <option value="Short-Answer">Short Answer</option>
          <option value="Code">Code</option>
        </select>
      </div>

      {loading ? (
        <div>Loading questions...</div>
      ) : (
        <div>
          <div className="results-info">
            <p>Found {pagination.total} questions</p>
            <p>Page {pagination.current} of {pagination.pages}</p>
          </div>

          <div className="questions-list">
            {questions.map(question => (
              <div key={question._id} className="question-item">
                <h4>{question.questionText}</h4>
                <div className="question-meta">
                  <span className="category">{question.category}</span>
                  <span className="difficulty">{question.difficulty}</span>
                  <span className="type">{question.questionType}</span>
                  <span className="points">{question.points} pts</span>
                </div>
                {question.explanation && (
                  <p className="explanation">{question.explanation}</p>
                )}
              </div>
            ))}
          </div>

          <div className="pagination">
            <button
              disabled={pagination.current <= 1}
              onClick={() => handleFilterChange('page', pagination.current - 1)}
            >
              Previous
            </button>
            <span>Page {pagination.current} of {pagination.pages}</span>
            <button
              disabled={pagination.current >= pagination.pages}
              onClick={() => handleFilterChange('page', pagination.current + 1)}
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
};
```

### 2.4 Add Questions to Test
```javascript
const addQuestionsToTest = async (testId, questionIds, points = 1) => {
  try {
    const response = await apiClient.post(`/${testId}/questions`, {
      questionIds,
      points
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Usage Example
const AddQuestionsToTest = ({ testId, onQuestionsAdded }) => {
  const [selectedQuestions, setSelectedQuestions] = useState([]);
  const [availableQuestions, setAvailableQuestions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [points, setPoints] = useState(1);

  const handleAddQuestions = async () => {
    if (selectedQuestions.length === 0) {
      alert('Please select at least one question');
      return;
    }

    setLoading(true);
    try {
      const result = await addQuestionsToTest(testId, selectedQuestions, points);
      onQuestionsAdded(result);
      setSelectedQuestions([]);
      alert(`${result.message}`);
    } catch (error) {
      console.error('Error adding questions:', error);
      alert('Error adding questions: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const toggleQuestionSelection = (questionId) => {
    setSelectedQuestions(prev =>
      prev.includes(questionId)
        ? prev.filter(id => id !== questionId)
        : [...prev, questionId]
    );
  };

  return (
    <div className="add-questions">
      <h3>Add Questions to Test</h3>

      <div className="points-selector">
        <label>Points per question:</label>
        <input
          type="number"
          min="1"
          max="10"
          value={points}
          onChange={(e) => setPoints(parseInt(e.target.value))}
        />
      </div>

      <div className="questions-selection">
        {availableQuestions.map(question => (
          <div key={question._id} className="question-checkbox">
            <input
              type="checkbox"
              checked={selectedQuestions.includes(question._id)}
              onChange={() => toggleQuestionSelection(question._id)}
            />
            <label>
              <strong>{question.questionText}</strong>
              <span className="meta">
                {question.category} | {question.difficulty} | {question.questionType}
              </span>
            </label>
          </div>
        ))}
      </div>

      <div className="actions">
        <p>Selected: {selectedQuestions.length} questions</p>
        <button
          onClick={handleAddQuestions}
          disabled={loading || selectedQuestions.length === 0}
        >
          {loading ? 'Adding...' : `Add ${selectedQuestions.length} Questions`}
        </button>
      </div>
    </div>
  );
};
```

### 2.5 Remove Questions from Test
```javascript
const removeQuestionsFromTest = async (testId, questionIds) => {
  try {
    const response = await apiClient.delete(`/${testId}/questions`, {
      data: { questionIds }
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Usage Example
const TestQuestionsList = ({ testId, questions, onQuestionsRemoved }) => {
  const [selectedForRemoval, setSelectedForRemoval] = useState([]);
  const [loading, setLoading] = useState(false);

  const handleRemoveQuestions = async () => {
    if (selectedForRemoval.length === 0) {
      alert('Please select questions to remove');
      return;
    }

    if (!window.confirm(`Remove ${selectedForRemoval.length} questions from test?`)) {
      return;
    }

    setLoading(true);
    try {
      const result = await removeQuestionsFromTest(testId, selectedForRemoval);
      onQuestionsRemoved(selectedForRemoval);
      setSelectedForRemoval([]);
      alert(result.message);
    } catch (error) {
      console.error('Error removing questions:', error);
      alert('Error removing questions: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const toggleQuestionForRemoval = (questionId) => {
    setSelectedForRemoval(prev =>
      prev.includes(questionId)
        ? prev.filter(id => id !== questionId)
        : [...prev, questionId]
    );
  };

  return (
    <div className="test-questions-list">
      <h3>Test Questions ({questions.length})</h3>

      {questions.map((q, index) => (
        <div key={q.questionId._id} className="question-item">
          <input
            type="checkbox"
            checked={selectedForRemoval.includes(q.questionId._id)}
            onChange={() => toggleQuestionForRemoval(q.questionId._id)}
          />
          <div className="question-content">
            <h4>Q{index + 1}: {q.questionId.questionText}</h4>
            <div className="question-meta">
              <span>Type: {q.questionId.questionType}</span>
              <span>Points: {q.points}</span>
              <span>Category: {q.questionId.category}</span>
              <span>Difficulty: {q.questionId.difficulty}</span>
            </div>
          </div>
        </div>
      ))}

      {selectedForRemoval.length > 0 && (
        <div className="removal-actions">
          <p>Selected {selectedForRemoval.length} questions for removal</p>
          <button
            onClick={handleRemoveQuestions}
            disabled={loading}
            className="remove-button"
          >
            {loading ? 'Removing...' : `Remove ${selectedForRemoval.length} Questions`}
          </button>
        </div>
      )}
    </div>
  );
};
```
