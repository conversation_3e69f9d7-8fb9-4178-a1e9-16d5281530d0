
require('dotenv').config();
const http = require('http')
const app = require('./app')
const connectDB = require('./src/config/db')

// * Load environment variables*
const PORT = process.env.PORT 
// * Connect to MongoDB *
connectDB()
    .then(() => {
        const server = http.createServer(app)
        server.listen(PORT, () => {
            console.log(` Server listening on http://localhost:${PORT}`)
        })
    })
    .catch((error) => {
        console.error(' MongoDB connection failed:', error)
        process.exit(1)
    })
