const mongoose = require('mongoose');

const ResultSchema = new mongoose.Schema(
    {
        candidateId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User',
            required: true,
            index: true
        },
        testId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Test',
            required: true,
            index: true
        },
        jobId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Job'
        }, // optional – if you want to link the job as well

        score: {
            type: Number,
            required: true,
            min: 0
        },
        totalScore: {
            type: Number,
            required: true,
            min: 0
        },
        percentage: {
            type: Number,
            min: 0,
            max: 100
        },

        answers: [
            {
                questionId: {
                    type: mongoose.Schema.Types.ObjectId,
                    ref: 'Question'
                },
                answer: String,
                isCorrect: Boolean,
                pointsEarned: Number
            }
        ],

        feedback: {
            type: String,
            maxlength: 1000
        },

        evaluatedBy: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User' // admin or company reviewer
        },

        tabSwitchCount: {
            type: Number,
            default: 0
        },
        suspiciousActivity: {
            type: Boolean,
            default: false
        }
    },
    { timestamps: true }
);

// compound index for quick look-ups
ResultSchema.index({ candidateId: 1, testId: 1 }, { unique: true });

module.exports = mongoose.model('Result', ResultSchema);