const Test = require('../../models/Test');
const Job = require('../../models/Job');
const User = require('../../models/User');

exports.getJobRecommendations = async (req, res, next) => {
    try {
        const studentId = req.user.id;

        const user = await User.findById(studentId).lean();
        if (!user) {
            return res.status(404).json({ success: false, message: 'User not found' });
        }

        const skills = user.skills || [];
        const interests = user.interests || [];

        const recommendedJobs = await Job.find({
            $or: [
                { requiredSkills: { $in: skills } },
                { domain: { $in: interests } }
            ],
            isActive: true
        })
            .limit(10)
            .lean();

        res.json({ success: true, data: recommendedJobs });
    } catch (error) {
        console.error(error);
        res.status(500).json({ success: false, message: 'Internal server error', error: error.message });
    }
};