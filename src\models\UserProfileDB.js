const mongoose = require("mongoose");

const Profile = new mongoose.Schema({
  Network: {
    type: String,
    required: true,
  },
  Username: {
    type: String,
    required: true,
  },
  ProfileLink: {
    type: String,
    required: true,
  },
  ProfileImage: {
    type: String,
    required: true,
  },
});

const Experience = new mongoose.Schema({
  Company: {
    type: String,
    required: true,
  },
  Position: {
    type: String,
    required: true,
  },
  StartDate: {
    type: Date,
    required: true,
  },
  EndDate: {
    type: Date,
    required: false,
  },
  Location: {
    type: String,
    required: false,
  },
  Website: {
    type: String,
    required: false,
  },
  Description: {
    type: String,
    required: false,
  },
});

const Education = new mongoose.Schema({
  Institution: {
    type: String,
    required: true,
  },
  Degree: {
    type: String,
    required: true,
  },
  StartDate: {
    type: Date,
    required: true,
  },
  EndDate: {
    type: Date,
    required: false,
  },
  Location: {
    type: String,
    required: false,
  },
});
const Skills = new mongoose.Schema({
  skill: {
    type: String,
    required: true,
  },
  proficiency: {
    type: String,
    required: true,
  },
  keywords: {
    type: [String],
    required: false,
  },
  description: {
    type: String,
    required: false,
  },
});

const Languages = new mongoose.Schema({
  Name: {
    type: String,
    required: true,
  },
  Proficiency: {
    type: String,
    required: true,
  },
  Description: {
    type: String,
    required: false,
  },
});

const Awards = new mongoose.Schema({
  Title: {
    type: String,
    required: true,
  },
  Issuer: {
    type: String,
    required: true,
  },
  Date: {
    type: Date,
    required: true,
  },
  Description: {
    type: String,
    required: false,
  },
});

const Certifications = new mongoose.Schema({
  Title: {
    type: String,
    required: true,
  },
  Issuer: {
    type: String,
    required: true,
  },
  Date: {
    type: Date,
    required: true,
  },
  Website: {
    type: String,
    required: false,
  },
  Description: {
    type: String,
    required: false,
  },
});

const Interests = new mongoose.Schema({
  Interest: {
    type: String,
    required: true,
  },
  Keywords: {
    type: [String],
    required: false,
  },
});

const Publications = new mongoose.Schema({
  Title: {
    type: String,
    required: true,
  },
  Publisher: {
    type: String,
    required: true,
  },
  Date: {
    type: Date,
    required: true,
  },
  Website: {
    type: String,
    required: false,
  },
  Description: {
    type: String,
    required: false,
  },
});

const Volunteering = new mongoose.Schema({
  Organization: {
    type: String,
    required: true,
  },
  Position: {
    type: String,
    required: true,
  },
  StartDate: {
    type: Date,
    required: true,
  },
  EndDate: {
    type: Date,
    required: false,
  },
  Location: {
    type: String,
    required: false,
  },
  Description: {
    type: String,
    required: false,
  },
});

const References = new mongoose.Schema({
  Name: {
    type: String,
    required: true,
  },
  Position: {
    type: String,
    required: true,
  },
  Company: {
    type: String,
    required: true,
  },
  Email: {
    type: String,
    required: true,
  },
  Phone: {
    type: String,
    required: false,
  },
});
const Project = new mongoose.Schema({
  Name: {
    type: String,
    required: true,
  },
  Description: {
    type: String,
    required: true,
  },
  Summery: {
    type: String,
  },
  Website: {
    type: String
  },


});
const Summary = new mongoose.Schema({
  text: {
    type: String,
    required: true,
  },
});

const UserProfileSchema = new mongoose.Schema({
  _id: { type: String, required: true, unique: true },
  ProfilePic: { type: String, required: false, default: "https://example.com/default-profile.png" },
  ProfilePicPublicId: { type: String, default: "" },
  Name: { type: String, required: true, default: "" },
  Email: { type: String, required: true, unique: true },
  Website: { type: String, default: "" },
  Phone: { type: String, default: "" },
  Location: { type: String, default: "Not specified" },
  Profiles: { type: [Profile], default: [] },
  Experience: { type: [Experience], default: [] },
  Education: { type: [Education], default: [] },
  Skills: { type: [Skills], default: [] },
  Languages: { type: [Languages], default: [] },
  Awards: { type: [Awards], default: [] },
  Certifications: { type: [Certifications], default: [] },
  Interests: { type: [Interests], default: [] },
  Publications: { type: [Publications], default: [] },
  Volunteering: { type: [Volunteering], default: [] },
  References: { type: [References], default: [] },
  Project: { type: [Project], default: [] },
  Summary: { type: [Summary], default: [] }
}, { timestamps: true });

module.exports = mongoose.model("UserProfile", UserProfileSchema);
