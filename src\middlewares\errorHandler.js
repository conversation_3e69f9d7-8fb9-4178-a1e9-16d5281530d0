
const logger = require('../config/logger'); // optional: <PERSON> logger

// !Error-handling middleware (must have 4 arguments)
function errorHandler(err, req, res, next) {
    // !Log the error if logger is available
    if (logger) {
        logger.error(`${req.method} ${req.url} - ${err.message}`, {
            stack: err.stack,
            status: err.status || 500
        });
    } else {
        console.error(err.stack || err.message);
    }

    // !Set status code
    const status = err.status || err.statusCode || 500;

    // !Build the error response
    const response = {
        success: false,
        status,
        error: err.message || 'Internal Server Error',
    };

    // !Include stack trace only in development mode
    if (process.env.NODE_ENV !== 'production') {
        response.stack = err.stack;
    }

    res.status(status).json(response);
}

module.exports = errorHandler;
