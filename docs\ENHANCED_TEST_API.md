# Enhanced Test Management API Documentation

## Overview
This document describes the enhanced test management API with improved security, job application-based candidate filtering, and question bundle management.

## Security Features
- **Job Application Verification**: Only candidates who have applied for associated jobs can be assigned to tests
- **Company Ownership Verification**: All operations verify that resources belong to the requesting company
- **Rate Limiting**: Prevents abuse of test creation and modification endpoints
- **Input Validation**: Comprehensive validation using Joi schemas
- **Parameter Validation**: ObjectId validation for all route parameters

## New API Endpoints

### Question Management

#### 1. Get Questions by Category
```
GET /api/tests/questions/by-category?category=Frontend
```
**Security**: Company ownership verification
**Response**: List of questions filtered by category

#### 2. Get Question Categories
```
GET /api/tests/questions/categories
```
**Security**: Company ownership verification
**Response**: Available categories with question counts

#### 3. Filter Questions
```
GET /api/tests/questions/filter?searchTerm=javascript&category=Frontend&difficulty=Medium&type=MCQ&page=1&limit=20
```
**Security**: Company ownership verification, input validation
**Response**: Paginated filtered questions

#### 4. Add Questions to Test
```
POST /api/tests/:testId/questions
Body: {
  "questionIds": ["questionId1", "questionId2"],
  "points": 1
}
```
**Security**: Test ownership verification, question ownership verification
**Response**: Success message with updated question count

#### 5. Remove Questions from Test
```
DELETE /api/tests/:testId/questions
Body: {
  "questionIds": ["questionId1", "questionId2"]
}
```
**Security**: Test ownership verification
**Response**: Success message with updated question count

### Question Bundle Management

#### 6. Create Question Bundle
```
POST /api/tests/question-bundles
Body: {
  "bundleName": "Frontend Basics",
  "description": "Basic frontend questions",
  "category": "Frontend",
  "difficulty": "Easy",
  "questionIds": ["questionId1", "questionId2"],
  "tags": ["html", "css", "javascript"]
}
```
**Security**: Rate limiting (3 per 5 minutes), input validation, question ownership verification
**Response**: Created bundle details

#### 7. Get Question Bundles
```
GET /api/tests/question-bundles?page=1&limit=10&category=Frontend&difficulty=Easy&search=basic
```
**Security**: Company ownership verification
**Response**: Paginated list of question bundles

#### 8. Get Question Bundle
```
GET /api/tests/question-bundles/:bundleId
```
**Security**: Bundle ownership verification
**Response**: Bundle details with populated questions

#### 9. Update Question Bundle
```
PUT /api/tests/question-bundles/:bundleId
Body: {
  "bundleName": "Updated Frontend Basics",
  "description": "Updated description",
  "questionIds": ["newQuestionId1", "newQuestionId2"]
}
```
**Security**: Bundle ownership verification, input validation
**Response**: Updated bundle details

#### 10. Delete Question Bundle
```
DELETE /api/tests/question-bundles/:bundleId
```
**Security**: Bundle ownership verification
**Response**: Success message

### Enhanced Candidate Management

#### 11. Get Candidates (Job Application Based)
```
GET /api/tests/candidates?page=1&limit=10&jobId=jobId&status=applied&sortBy=appliedAt&sortOrder=desc
```
**Security**: Company ownership verification, only shows candidates who applied for company jobs
**Response**: Paginated list of candidates with application details

#### 12. Search Candidates
```
GET /api/tests/candidates/search?search=john&experience=1-3&skills=javascript&location=bangalore&status=applied&jobId=jobId&page=1&limit=10
```
**Security**: Company ownership verification, input validation, job application filtering
**Response**: Filtered candidates who applied for company jobs

#### 13. Get Available Candidates for Test
```
GET /api/tests/candidates/available-for-test?testId=testId
```
**Security**: Test ownership verification, job application verification
**Response**: Candidates who applied for test's associated jobs and are not yet assigned

#### 14. Direct Candidate Assignment
```
POST /api/tests/:testId/assign-candidates
Body: {
  "candidateIds": ["candidateId1", "candidateId2"]
}
```
**Security**: Job application verification, test ownership verification
**Response**: Assignment confirmation with counts

## Enhanced Security Features

### 1. Job Application Verification
- Verifies candidates have applied for jobs associated with the test
- Prevents unauthorized candidate assignment
- Returns detailed error messages for invalid assignments

### 2. Company Ownership Verification
- All operations verify resource ownership
- Prevents cross-company data access
- Consistent security across all endpoints

### 3. Rate Limiting
- Test creation: 5 per 5 minutes
- Bundle creation: 3 per 5 minutes
- Prevents API abuse and spam

### 4. Input Validation
- Comprehensive Joi schemas for all inputs
- Parameter validation for ObjectIds
- Sanitization of string inputs
- Range validation for numeric inputs

## Error Responses

### Validation Errors
```json
{
  "error": "Validation failed",
  "details": [
    {
      "field": "testName",
      "message": "testName is required"
    }
  ]
}
```

### Security Errors
```json
{
  "error": "Some candidates have not applied for the required jobs",
  "invalidCandidates": ["candidateId1"],
  "requiredJobs": [
    {
      "id": "jobId1",
      "title": "Frontend Developer"
    }
  ],
  "message": "Only candidates who have applied for the associated jobs can be assigned to this test"
}
```

### Rate Limiting Errors
```json
{
  "error": "Too many test operations. Please try again later.",
  "retryAfter": 300
}
```

## Database Models

### QuestionBundle Model
```javascript
{
  companyId: ObjectId,
  createdBy: ObjectId,
  bundleName: String,
  description: String,
  category: String,
  difficulty: String,
  questions: [{
    questionId: ObjectId,
    points: Number,
    order: Number
  }],
  totalQuestions: Number,
  totalPoints: Number,
  estimatedDuration: Number,
  tags: [String],
  isActive: Boolean,
  usageCount: Number,
  lastUsedAt: Date
}
```

## Migration Notes

### Existing Functionality
- All existing test endpoints remain functional
- Enhanced security added to existing endpoints
- Backward compatibility maintained

### New Dependencies
- Joi for validation
- Enhanced middleware for security
- New database indexes for performance

## Performance Optimizations

### Database Indexes
- Company-based filtering indexes
- Text search indexes for bundles
- Composite indexes for common queries

### Aggregation Pipelines
- Efficient candidate filtering
- Job application verification
- Reduced database queries

## Usage Examples

### Creating a Test with Job Application Verification
1. Create test with associated jobs
2. Only candidates who applied for those jobs can be assigned
3. System automatically verifies applications before assignment

### Question Bundle Workflow
1. Create bundle with categorized questions
2. Reuse bundle across multiple tests
3. Track usage statistics
4. Update bundle as needed

### Secure Candidate Management
1. Search only shows candidates who applied for company jobs
2. Filter by application status and job requirements
3. Assign candidates with automatic verification
