const express = require('express');
const router = express.Router();
const authMiddleware = require('../middlewares/authMiddleware');
const roleMiddleware = require('../middlewares/roleMiddleware');
const questionController = require('../controllers/questionController');

// Auth + company/admin only
router.use(authMiddleware);
router.use(roleMiddleware(['company', 'admin']));

// Create a new question
router.post('/', questionController.createQuestion);

// Get all questions with filter/sort
router.get('/', questionController.getQuestions);

// Get a single question
router.get('/:questionId', questionController.getQuestionById);

// Update a question
router.patch('/:questionId', questionController.updateQuestion);

// Delete a question
router.delete('/:questionId', questionController.deleteQuestion);

module.exports = router;
