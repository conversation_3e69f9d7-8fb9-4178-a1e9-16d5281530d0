const UserProfileDB = require("../models/UserProfileDB");

exports.testAPI = (req, res) => {
    res.send("UpdateInfo API is working");
};

exports.updateWebsite = async (req, res) => {
    const { website } = req.body;
    if (!website) return res.status(400).json({ error: "Website is required" });

    try {
        const user = await UserProfileDB.findOneAndUpdate(
            { _id: req.user.email },
            { Website: website },
            { new: true }
        );
        res.status(200).json({ message: "Website updated", user });
    } catch {
        res.status(500).json({ error: "Internal server error" });
    }
};

exports.updatePhone = async (req, res) => {
    const { phone } = req.body;
    if (!phone) return res.status(400).json({ error: "Phone is required" });

    try {
        const user = await UserProfileDB.findOneAndUpdate(
            { _id: req.user.email },
            { Phone: phone },
            { new: true }
        );
        res.status(200).json({ message: "Phone updated", user });
    } catch {
        res.status(500).json({ error: "Internal server error" });
    }
};

exports.updateLocation = async (req, res) => {
    const { location } = req.body;
    if (!location) return res.status(400).json({ error: "Location is required" });

    try {
        const user = await UserProfileDB.findOneAndUpdate(
            { _id: req.user.email },
            { Location: location },
            { new: true }
        );
        res.status(200).json({ message: "Location updated", user });
    } catch {
        res.status(500).json({ error: "Internal server error" });
    }
};
exports.updateProfile = async (req, res) => {
    const { id, Network, Username, ProfileLink, ProfileImage } = req.body;
    if (!id) return res.status(400).json({ error: "Profile ID required" });

    try {
        const user = await UserProfileDB.findOneAndUpdate(
            { _id: req.user.email, "Profiles._id": id },
            {
                $set: {
                    "Profiles.$.Network": Network,
                    "Profiles.$.Username": Username,
                    "Profiles.$.ProfileLink": ProfileLink,
                    "Profiles.$.ProfileImage": ProfileImage,
                },
            },
            { new: true }
        );
        res.status(200).json({ message: "Profile updated", user });
    } catch {
        res.status(500).json({ error: "Internal server error" });
    }
};

exports.updateExperience = async (req, res) => {
    const { id, Company, Position, StartDate, EndDate, Description } = req.body;
    if (!id) return res.status(400).json({ error: "Experience ID required" });

    try {
        const user = await UserProfileDB.findOneAndUpdate(
            { _id: req.user.email, "Experience._id": id },
            {
                $set: {
                    "Experience.$.Company": Company,
                    "Experience.$.Position": Position,
                    "Experience.$.StartDate": StartDate,
                    "Experience.$.EndDate": EndDate,
                    "Experience.$.Description": Description,
                },
            },
            { new: true }
        );
        res.status(200).json({ message: "Experience updated", user });
    } catch {
        res.status(500).json({ error: "Internal server error" });
    }
};

exports.updateEducation = async (req, res) => {
    const { id, Institution, Degree, StartDate, EndDate, Field } = req.body;
    if (!id) return res.status(400).json({ error: "Education ID required" });

    try {
        const user = await UserProfileDB.findOneAndUpdate(
            { _id: req.user.email, "Education._id": id },
            {
                $set: {
                    "Education.$.Institution": Institution,
                    "Education.$.Degree": Degree,
                    "Education.$.StartDate": StartDate,
                    "Education.$.EndDate": EndDate,
                    "Education.$.Field": Field,
                },
            },
            { new: true }
        );
        res.status(200).json({ message: "Education updated", user });
    } catch {
        res.status(500).json({ error: "Internal server error" });
    }
};

exports.updateSkill = async (req, res) => {
    const { id, Skill, Proficiency } = req.body;
    if (!id) return res.status(400).json({ error: "Skill ID required" });

    try {
        const user = await UserProfileDB.findOneAndUpdate(
            { _id: req.user.email, "Skills._id": id },
            {
                $set: {
                    "Skills.$.Skill": Skill,
                    "Skills.$.Proficiency": Proficiency,
                },
            },
            { new: true }
        );
        res.status(200).json({ message: "Skill updated", user });
    } catch {
        res.status(500).json({ error: "Internal server error" });
    }
};

exports.updateLanguage = async (req, res) => {
    const { id, Name, Proficiency } = req.body;
    if (!id) return res.status(400).json({ error: "Language ID required" });

    try {
        const user = await UserProfileDB.findOneAndUpdate(
            { _id: req.user.email, "Languages._id": id },
            {
                $set: {
                    "Languages.$.Name": Name,
                    "Languages.$.Proficiency": Proficiency,
                },
            },
            { new: true }
        );
        res.status(200).json({ message: "Language updated", user });
    } catch {
        res.status(500).json({ error: "Internal server error" });
    }
};

exports.updateAward = async (req, res) => {
    const { id, Title, Issuer, Date } = req.body;
    if (!id) return res.status(400).json({ error: "Award ID required" });

    try {
        const user = await UserProfileDB.findOneAndUpdate(
            { _id: req.user.email, "Awards._id": id },
            {
                $set: {
                    "Awards.$.Title": Title,
                    "Awards.$.Issuer": Issuer,
                    "Awards.$.Date": Date,
                },
            },
            { new: true }
        );
        res.status(200).json({ message: "Award updated", user });
    } catch {
        res.status(500).json({ error: "Internal server error" });
    }
};

exports.updateCertification = async (req, res) => {
    const { id, Title, Issuer, Date } = req.body;
    if (!id) return res.status(400).json({ error: "Certification ID required" });

    try {
        const user = await UserProfileDB.findOneAndUpdate(
            { _id: req.user.email, "Certifications._id": id },
            {
                $set: {
                    "Certifications.$.Title": Title,
                    "Certifications.$.Issuer": Issuer,
                    "Certifications.$.Date": Date,
                },
            },
            { new: true }
        );
        res.status(200).json({ message: "Certification updated", user });
    } catch {
        res.status(500).json({ error: "Internal server error" });
    }
};

exports.updateInterest = async (req, res) => {
    const { id, Interest } = req.body;
    if (!id) return res.status(400).json({ error: "Interest ID required" });

    try {
        const user = await UserProfileDB.findOneAndUpdate(
            { _id: req.user.email, "Interests._id": id },
            {
                $set: {
                    "Interests.$.Interest": Interest,
                },
            },
            { new: true }
        );
        res.status(200).json({ message: "Interest updated", user });
    } catch {
        res.status(500).json({ error: "Internal server error" });
    }
};

exports.updatePublication = async (req, res) => {
    const { id, Title, Publisher, Date } = req.body;
    if (!id) return res.status(400).json({ error: "Publication ID required" });

    try {
        const user = await UserProfileDB.findOneAndUpdate(
            { _id: req.user.email, "Publications._id": id },
            {
                $set: {
                    "Publications.$.Title": Title,
                    "Publications.$.Publisher": Publisher,
                    "Publications.$.Date": Date,
                },
            },
            { new: true }
        );
        res.status(200).json({ message: "Publication updated", user });
    } catch {
        res.status(500).json({ error: "Internal server error" });
    }
};

exports.updateVolunteering = async (req, res) => {
    const { id, Organization, Position, StartDate, EndDate } = req.body;
    if (!id) return res.status(400).json({ error: "Volunteering ID required" });

    try {
        const user = await UserProfileDB.findOneAndUpdate(
            { _id: req.user.email, "Volunteering._id": id },
            {
                $set: {
                    "Volunteering.$.Organization": Organization,
                    "Volunteering.$.Position": Position,
                    "Volunteering.$.StartDate": StartDate,
                    "Volunteering.$.EndDate": EndDate,
                },
            },
            { new: true }
        );
        res.status(200).json({ message: "Volunteering updated", user });
    } catch {
        res.status(500).json({ error: "Internal server error" });
    }
};

exports.updateReference = async (req, res) => {
    const { id, Name, Position, Company, Email } = req.body;
    if (!id) return res.status(400).json({ error: "Reference ID required" });

    try {
        const user = await UserProfileDB.findOneAndUpdate(
            { _id: req.user.email, 'References._id': id },
            {
                $set: {
                    'References.$.Name': Name,
                    'References.$.Position': Position,
                    'References.$.Company': Company,
                    'References.$.Email': Email,
                },
            },
            { new: true }
        );

        res.status(200).json({ message: "Reference updated", user });
    } catch {
        res.status(500).json({ error: "Internal server error" });
    }
};
