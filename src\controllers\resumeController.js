const ResumeProfile = require('../models/UserResumeDB');


exports.createResume = async (req, res) => {
  try {
    const newResume = new ResumeProfile({
      ...req.body,
      Email: req.user.email,
      Title: req.body.Title
    });
    const email = req.user.email;
    const existingResume = await ResumeProfile.findOne({ Email: email });

    if (existingResume) {
      return res.status(400).json({ error: 'Resume already exists' });
    }

    const saved = await newResume.save();
    res.status(201).json({ message: 'Resume created successfully', resume: saved });
  } catch (err) {
    res.status(500).json({ error: 'Failed to create resume', details: err.message });
  }
};

exports.getUserResumes = async (req, res) => {
  try {
    const resumes = await ResumeProfile.find({ Email: req.user.email });
    res.status(200).json(resumes);
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch resumes' });
  }
};

exports.getResumeById = async (req, res) => {
  const { id } = req.params;

  try {
    const resume = await ResumeProfile.findById(id);

    if (!resume) {
      return res.status(404).json({ error: 'Resume not found' });
    }

    res.status(200).json({ resume });
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch resume', details: err.message });
  }
};

exports.updateResume = async (req, res) => {
  try {
    const updated = await ResumeProfile.findOneAndUpdate(
      { _id: req.params.id, Email: req.user.email },
      req.body,
      { new: true, runValidators: true }
    );
    if (!updated) return res.status(404).json({ error: 'Resume not found or unauthorized' });
    res.status(200).json({ message: 'Resume updated successfully', resume: updated });
  } catch (err) {
    res.status(500).json({ error: 'Failed to update resume', details: err.message });
  }
};

exports.deleteResume = async (req, res) => {
  try {
    const deleted = await ResumeProfile.findOneAndDelete({
      _id: req.params.id,
      Email: req.user.email
    });
    if (!deleted) return res.status(404).json({ error: 'Resume not found or unauthorized' });
    res.status(200).json({ message: 'Resume deleted successfully' });
  } catch (err) {
    res.status(500).json({ error: 'Failed to delete resume' });
  }
};

exports.duplicateResume = async (req, res) => {
  try {
    const original = await ResumeProfile.findOne({
      _id: req.params.id,
      Email: req.user.email
    });

    if (!original) return res.status(404).json({ error: 'Resume not found' });

    const clone = new ResumeProfile({
      ...original.toObject(),
      _id: undefined,
      Title: original.Title + ' (Copy)',
      isPublished: false,
      PublishURL: null,
      PDFLink: null,
      LastExported: null,
      createdAt: undefined,
      updatedAt: undefined
    });

    const saved = await clone.save();
    res.status(201).json({ message: 'Resume duplicated', resume: saved });
  } catch (err) {
    res.status(500).json({ error: 'Failed to duplicate resume', details: err.message });
  }
};

