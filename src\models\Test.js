const mongoose = require('mongoose');

const TestSchema = new mongoose.Schema({
    companyId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Company',
        required: true
    },
    associatedJobs: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Job'
    }],

    testName: {
        type: String,
        required: true,
        trim: true
    },
    description: {
        type: String,
        maxlength: 500
    },
    questions: [{
        questionId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Question',
            required: true
        },
        points: {
            type: Number,
            default: 1
        },
        // Optional: snapshot of question data at the time of test creation
        questionText: String,
        optionsSnapshot: [String]
    }],
    duration: {
        type: Number,
        required: true,
        min: 5,
        max: 300
    },
    totalPoints: {
        type: Number,
        default: 0
    },
    passingScore: {
        type: Number,
        default: 50
    },
    scheduledDate: {
        type: Date,
        required: true
    },
    endDate: {
        type: Date,
        required: true
    },
    isActive: {
        type: Boolean,
        default: true
    },
    flaggedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User', default: null },
    flaggedAt: { type: Date, default: null },
    flagReason: { type: String, default: null },
    instructions: {
        type: String,
        maxlength: 1000
    },
    allowedAttempts: {
        type: Number,
        default: 1
    },
    randomizeQuestions: {
        type: Boolean,
        default: false
    },
    showResults: {
        type: Boolean,
        default: false
    },
    participants: [{
        candidateId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User'
        },
        startedAt: Date,
        submittedAt: Date,
        score: Number,
        totalScore: Number,
        percentage: Number,
        status: {
            type: String,
            enum: ['assigned', 'started', 'submitted', 'evaluated'],
            default: 'assigned'
        },
        answers: [{
            questionId: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'Question'
            },
            answer: String,
            isCorrect: Boolean,
            pointsEarned: Number
        }],
        feedback: String,
        evaluatedBy: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User' // Admin or company reviewer
        },
        tabSwitchCount: {
            type: Number,
            default: 0
        },
        suspiciousActivity: {
            type: Boolean,
            default: false
        }
    }]
}, {
    timestamps: true
});
TestSchema.index({ 'participants.candidateId': 1 });
TestSchema.pre('save', function (next) {
    if (this.questions && this.questions.length > 0) {
        this.totalPoints = this.questions.reduce((sum, q) => sum + (q.points || 0), 0);
    }
    next();
});
module.exports = mongoose.model('Test', TestSchema);
