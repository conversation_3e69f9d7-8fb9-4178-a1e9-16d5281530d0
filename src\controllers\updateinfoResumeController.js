const ResumeProfile = require('../models/UserResumeDB');

exports.testAPI = (req, res) => {
  res.send('Resume update API is working');
};
const updateSubDocument = async (res, resumeId, arrayField, subDocId, updateData, label) => {
  try {
    const updateQuery = {};
    for (let key in updateData) {
      updateQuery[`${arrayField}.$.${key}`] = updateData[key];
    }

    const result = await ResumeProfile.findOneAndUpdate(
      { _id: resumeId, [`${arrayField}._id`]: subDocId },
      { $set: updateQuery },
      { new: true, runValidators: true }
    );

    if (!result) {
      return res.status(404).json({ error: `${label} not found` });
    }

    res.status(200).json({ message: `${label} updated`, updated: result });
  } catch (err) {
    res.status(500).json({ error: 'Internal server error', details: err.message });
  }
};

exports.updateProfile = (req, res) => {
  updateSubDocument(res, req.params.resumeId, 'Profiles', req.params.id, req.body, 'Profile');
};

exports.updateExperience = (req, res) => {
  updateSubDocument(res, req.params.resumeId, 'Experience', req.params.id, req.body, 'Experience');
};

exports.updateEducation = (req, res) => {
  updateSubDocument(res, req.params.resumeId, 'Education', req.params.id, req.body, 'Education');
};

exports.updateSkill = (req, res) => {
  updateSubDocument(res, req.params.resumeId, 'Skills', req.params.id, req.body, 'Skill');
};

exports.updateLanguage = (req, res) => {
  updateSubDocument(res, req.params.resumeId, 'Languages', req.params.id, req.body, 'Language');
};

exports.updateAward = (req, res) => {
  updateSubDocument(res, req.params.resumeId, 'Awards', req.params.id, req.body, 'Award');
};

exports.updateCertification = (req, res) => {
  updateSubDocument(res, req.params.resumeId, 'Certifications', req.params.id, req.body, 'Certification');
};

exports.updateInterest = (req, res) => {
  updateSubDocument(res, req.params.resumeId, 'Interests', req.params.id, req.body, 'Interest');
};

exports.updatePublication = (req, res) => {
  updateSubDocument(res, req.params.resumeId, 'Publications', req.params.id, req.body, 'Publication');
};

exports.updateVolunteering = (req, res) => {
  updateSubDocument(res, req.params.resumeId, 'Volunteering', req.params.id, req.body, 'Volunteering');
};

exports.updateReference = (req, res) => {
  updateSubDocument(res, req.params.resumeId, 'References', req.params.id, req.body, 'Reference');
};
exports.updateproject = (req, res) => {
  updateSubDocument(res, req.params.resumeId, 'Projects', req.params.id, req.body, 'Project');
};