const mongoose = require('mongoose');

const JobSchema = new mongoose.Schema({
    companyId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Company',
        required: true
    },
    title: {
        type: String,
        required: true,
        trim: true
    },
    description: {
        type: String,
        required: true,
        maxlength: 2000
    },
    requirements: [{
        type: String,
        trim: true
    }],
    techStack: [{
        type: String,
        trim: true
    }],
    category: {
        type: String,
        enum: ['Frontend', 'Backend', 'Full Stack', 'Data Science', 'DevOps', 'Mobile', 'UI/UX', 'QA', 'Other'],
        required: true
    },
    jobType: {
        type: String,
        enum: ['Full-time', 'Part-time', 'Contract', 'Internship'],
        default: 'Full-time'
    },
    experienceLevel: {
        type: String,
        enum: ['Entry', 'Mid', 'Senior', 'Lead'],
        default: 'Entry'
    },
    salary: {
        min: Number,
        max: Number,
        currency: {
            type: String,
            default: 'INR'
        }
    },
    location: {
        type: String,
        required: true
    },
    workMode: {
        type: String,
        enum: ['Remote', 'On-site', 'Hybrid'],
        default: 'On-site'
    },
    applicationDeadline: {
        type: Date,
        required: true
    },
    maxApplications: {
        type: Number,
        default: 100
    },
    currentApplications: {
        type: Number,
        default: 0
    },
    isActive: {
        type: Boolean,
        default: true
    },
    flaggedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        default: null
    },
    flaggedAt: {
        type: Date,
        default: null
    },
    flagReason: {
        type: String,
        default: null
    },
    hasTest: {
        type: Boolean,
        default: false
    },
    testId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Test',
        default: null
    },
    applicants: [{
        candidateId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User'
        },
        appliedAt: {
            type: Date,
            default: Date.now
        },
        status: {
            type: String,
            enum: ['applied', 'test_pending', 'test_completed', 'shortlisted', 'rejected'],
            default: 'applied'
        },
        testScore: {
            type: Number,
            default: null
        }
    }]
}, {
    timestamps: true
});

module.exports = mongoose.model('Job', JobSchema);
