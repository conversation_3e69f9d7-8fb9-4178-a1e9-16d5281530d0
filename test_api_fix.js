// Test script to verify the API fix
const axios = require('axios');

const API_BASE_URL = 'http://localhost:5000/api';

// Test configuration
const testConfig = {
  // Replace with actual values from your database
  authToken: 'your-auth-token-here',
  testId: '687f67966fad6a0ea16667e0',
  questionIds: ['question-id-1', 'question-id-2']
};

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${testConfig.authToken}`
  },
  withCredentials: true
});

async function testAddQuestionsToTest() {
  try {
    console.log('Testing Add Questions to Test API...');
    
    const response = await apiClient.post(`/tests/${testConfig.testId}/questions`, {
      questionIds: testConfig.questionIds,
      points: 2
    });
    
    console.log('✅ Success:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Error:', error.response?.data || error.message);
    return false;
  }
}

async function testGetTests() {
  try {
    console.log('Testing Get All Tests API...');
    
    const response = await apiClient.get('/tests/');
    
    console.log('✅ Success: Found', response.data.count, 'tests');
    return true;
  } catch (error) {
    console.log('❌ Error:', error.response?.data || error.message);
    return false;
  }
}

async function testGetTestDetails() {
  try {
    console.log('Testing Get Test Details API...');
    
    const response = await apiClient.get(`/tests/${testConfig.testId}`);
    
    console.log('✅ Success: Test found -', response.data.test.testName);
    return true;
  } catch (error) {
    console.log('❌ Error:', error.response?.data || error.message);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting API Tests...\n');
  
  const results = {
    getTests: await testGetTests(),
    getTestDetails: await testGetTestDetails(),
    addQuestions: await testAddQuestionsToTest()
  };
  
  console.log('\n📊 Test Results:');
  console.log('- Get Tests:', results.getTests ? '✅ PASS' : '❌ FAIL');
  console.log('- Get Test Details:', results.getTestDetails ? '✅ PASS' : '❌ FAIL');
  console.log('- Add Questions:', results.addQuestions ? '✅ PASS' : '❌ FAIL');
  
  const passCount = Object.values(results).filter(Boolean).length;
  console.log(`\n🎯 Overall: ${passCount}/3 tests passed`);
}

// Instructions for manual testing
console.log(`
📋 MANUAL TESTING INSTRUCTIONS:

1. Update the testConfig object above with:
   - Your actual auth token
   - A valid test ID from your database
   - Valid question IDs

2. Make sure your server is running on http://localhost:5000

3. Run this script: node test_api_fix.js

4. Or test manually with curl:

   # Get all tests
   curl -X GET "http://localhost:5000/api/tests/" \\
     -H "Authorization: Bearer YOUR_TOKEN" \\
     -H "Content-Type: application/json"

   # Get test details
   curl -X GET "http://localhost:5000/api/tests/YOUR_TEST_ID" \\
     -H "Authorization: Bearer YOUR_TOKEN" \\
     -H "Content-Type: application/json"

   # Add questions to test
   curl -X POST "http://localhost:5000/api/tests/YOUR_TEST_ID/questions" \\
     -H "Authorization: Bearer YOUR_TOKEN" \\
     -H "Content-Type: application/json" \\
     -d '{"questionIds": ["QUESTION_ID_1", "QUESTION_ID_2"], "points": 2}'

5. Frontend Integration Example:

   const addQuestionsToTest = async (testId, questionIds, points = 1) => {
     try {
       const response = await fetch(\`/api/tests/\${testId}/questions\`, {
         method: 'POST',
         headers: {
           'Content-Type': 'application/json',
           'Authorization': \`Bearer \${localStorage.getItem('authToken')}\`
         },
         body: JSON.stringify({ questionIds, points })
       });
       
       if (!response.ok) {
         throw new Error(\`HTTP error! status: \${response.status}\`);
       }
       
       const data = await response.json();
       console.log('Questions added:', data);
       return data;
     } catch (error) {
       console.error('Error adding questions:', error);
       throw error;
     }
   };

   // Usage
   addQuestionsToTest('testId123', ['questionId1', 'questionId2'], 2)
     .then(result => console.log('Success:', result))
     .catch(error => console.error('Failed:', error));
`);

// Uncomment the line below to run the tests (after updating testConfig)
// runTests();
