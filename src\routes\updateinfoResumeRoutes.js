const express = require('express');
const router = express.Router();
const authMiddleware = require('../middlewares/authMiddleware');
const controller = require('../controllers/updateinfoResumeController');

router.get('/', controller.testAPI);

router.patch('/:resumeId/updateprofile/:id', authMiddleware, controller.updateProfile);
router.patch('/:resumeId/updateexperience/:id', authMiddleware, controller.updateExperience);
router.patch('/:resumeId/updateeducation/:id', authMiddleware, controller.updateEducation);
router.patch('/:resumeId/updateskills/:id', authMiddleware, controller.updateSkill);
router.patch('/:resumeId/updatelanguages/:id', authMiddleware, controller.updateLanguage);
router.patch('/:resumeId/updateawards/:id', authMiddleware, controller.updateAward);
router.patch('/:resumeId/updatecertifications/:id', authMiddleware, controller.updateCertification);
router.patch('/:resumeId/updateinterests/:id', authMiddleware, controller.updateInterest);
router.patch('/:resumeId/updatepublications/:id', authMiddleware, controller.updatePublication);
router.patch('/:resumeId/updatevolunteering/:id', authMiddleware, controller.updateVolunteering);
router.patch('/:resumeId/updatereferences/:id', authMiddleware, controller.updateReference);
router.patch('/:resumeId/updateprojects/:id', authMiddleware, controller.updateproject);
module.exports = router;

