const express = require('express');
const adminRoute = express.Router();

const adminController = require('../controllers/adminController');
const authMiddleware = require('../middlewares/authMiddleware');
const isAdmin = require('../middlewares/isAdmin');
const AdminLog = require('../models/AdminLog');

adminRoute.use(authMiddleware, isAdmin);


//!REsume Management ROute --------------------------------->
adminRoute.get('/all', adminController.getAllResumes);
adminRoute.get('/resume/:resumeId', adminController.getResumeById);
adminRoute.delete('/resume/:resumeId', adminController.deleteResume);
adminRoute.get('/user/:userId', adminController.getResumesByUser);
adminRoute.get('/users', adminController.getAllUsers);

//!  User Management Routes ----------------------------->
adminRoute.get('/users/:userId', adminController.getUserDetails);
adminRoute.patch('/users/:userId/activate', adminController.activateOrDeactivateUser);
adminRoute.delete('/users/:userId', adminController.deleteUser);


// !====  Company Management ====
adminRoute.get('/companies/pending', adminController.listPendingCompanies);
// adminRoute.patch('/companies/:companyId/approve', adminController.approveCompany);
// adminRoute.patch('/companies/:companyId/reject', adminController.rejectCompany);
adminRoute.delete('/companies/:companyId', adminController.deleteCompany);

//! ====  Job Moderation ====
adminRoute.get('/jobs', adminController.getAllJobs);
adminRoute.patch('/jobs/:jobId/flag', adminController.flagJob);
adminRoute.delete('/jobs/:jobId', adminController.deleteJob);

//! ==== Test Moderation ====
adminRoute.get('/tests', adminController.getAllTests);
adminRoute.patch('/tests/:testId/flag', adminController.flagTest);
adminRoute.delete('/tests/:testId', adminController.deleteTest);

// !====  Platform Stats & Reports ====
// adminRoute.get('/stats', adminController.getPlatformStats);
// adminRoute.get('/reports', adminController.exportReports);

//! ==== Notification Management ====
// adminRoute.post('/notifications', adminController.sendNotificationToAll);
// adminRoute.get('/notifications', adminController.getAllNotifications);


adminRoute.get("/logs", async (req, res) => {
    try {
        const logs = await AdminLog.find().sort({ timestamp: -1 }).limit(100);
        res.json({ logs });
    } catch (err) {
        res.status(500).json({ error: "Failed to fetch logs", details: err.message });
    }
});

module.exports = adminRoute;
