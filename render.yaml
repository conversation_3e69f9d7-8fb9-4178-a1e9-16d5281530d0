services:
  - type: web
    name: resumebuilder-backend
    env: node
    plan: free
    buildCommand: |
      npm install
      npx puppeteer browsers install chrome
    startCommand: npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 5000
      - key: PUPPETEER_EXECUTABLE_PATH
        value: /opt/render/project/.cache/puppeteer/chrome/linux-127.0.6533.88/chrome-linux64/chrome
      - key: PUPPETEER_SKIP_CHROMIUM_DOWNLOAD
        value: false
