const express = require('express')
const cors = require('cors')
const cookieParser = require('cookie-parser');
const helmet = require('helmet')
const morgan = require('morgan')
const rateLimiter = require('./src/middlewares/rateLimiter')
const errorHandler = require('./src/middlewares/errorHandler')

const session = require('express-session')
const passport = require('passport')
require('./src/config/passport')
const app = express()

// Core middlewares
const allowedOrigins = [
    'http://localhost:5173',
    'https://resume-builder-lilac-eta.vercel.app', // ✅ add your Vercel frontend URL here
];

app.use(cors({
    origin: function (origin, callback) {
        if (!origin || allowedOrigins.includes(origin)) {
            callback(null, true);
        } else {
            callback(new Error('Not allowed by CORS'));
        }
    },
    credentials: true, // ✅ required for cookies
}));
app.use(helmet())
app.use(morgan('dev'))
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true }))
//! Rate limit 
app.use(rateLimiter)
app.use(cookieParser());
app.use(session({ secret: 'your-session-secret', resave: false, saveUninitialized: false }))
app.use(passport.initialize())
app.use(passport.session())


// ! Auth Routes ---------------->
const authRoutes = require('./src/routes/authRoutes');
app.use('/api/auth', authRoutes);



// ! Main Routes ---------------->
const addinfoRoutes = require('./src/routes/addinfoRoutes')
const deleteinfoRoutes = require('./src/routes/deleteinfoRoutes')
const updateinfoRoutes = require('./src/routes/updateinfoRoutes');
app.use('/api/info/add', addinfoRoutes);
app.use('/api/info/update', updateinfoRoutes);
app.use('/api/info/delete', deleteinfoRoutes);



//! Resume Routes --------------------->
const resumeRoutes = require('./src/routes/resumeRoutes');
const addinfoResume = require('./src/routes/addinforResumeRoutes');
const updateinfoResume = require('./src/routes/updateinfoResumeRoutes');
const deleteinfoResume = require('./src/routes/deleteinfoResumeRoutes');
app.use('/api/resume', resumeRoutes);
app.use('/api/resume/addinfo', addinfoResume);
app.use('/api/resume/updateinfo', updateinfoResume);
app.use('/api/resume/deleteinfo', deleteinfoResume);

//! Admin Route ---------------->


const adminRoute = require('./src/routes/adminRoutes');
app.use('/api/admin', adminRoute);


//! Company Route ---------------->
const companyRoutes = require('./src/routes/companyRoutes');
app.use('/api/company', companyRoutes);
const questionRoutes = require('./src/routes/questionRoutes');
app.use('/api/questions', questionRoutes);

const testRoutes = require('./src/routes/testRoutes');
app.use('/api/tests', testRoutes);


//! Student Route ---------------->
const studentRoutes = require('./src/routes/stuentRoutes');
app.use('/api/student', studentRoutes);


const pdfRoute = require('./src/routes/pdfRoutes');

app.use('/api/pdf', pdfRoute)

// *Error Handling 
app.use(errorHandler)


app.get('/', (req, res) => {
    res.send('Welcome to the Resume Builder API')
})

module.exports = app
