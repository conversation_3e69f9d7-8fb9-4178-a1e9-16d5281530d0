const express = require('express');
const router = express.Router();
const controller = require('../controllers/addinfoResumeController');
const authMiddleware = require('../middlewares/authMiddleware');
const upload = require('../middlewares/upload');

// *Test route
router.get('/', controller.testAPI);

// ! Image upload & delete
router.post('/upload-image/:id', authMiddleware, upload, controller.uploadImage);
router.delete('/delete-image/:id', authMiddleware, controller.deleteImage);

// ! Resume fields
router.post('/addprofile/:id', authMiddleware, controller.addProfile);
router.post('/addexperience/:id', authMiddleware, controller.addExperience);
router.post('/addeducation/:id', authMiddleware, controller.addEducation);
router.post('/addskills/:id', authMiddleware, controller.addSkill);
router.post('/addlanguages/:id', authMiddleware, controller.addLanguage);
router.post('/addawards/:id', authMiddleware, controller.addAward);
router.post('/addcertifications/:id', authMiddleware, controller.addCertification);
router.post('/addinterests/:id', authMiddleware, controller.addInterest);
router.post('/addpublications/:id', authMiddleware, controller.addPublication);
router.post('/addvolunteering/:id', authMiddleware, controller.addVolunteering);
router.post('/addreferences/:id', authMiddleware, controller.addReference);
router.post('/addprojects/:id', authMiddleware, controller.addProject);
router.post('/addsummery/:id', authMiddleware, controller.addSummary);
router.patch('/update-basics/:id', authMiddleware, controller.updateBasicsField);
router.post('/addsummery/:id', authMiddleware, controller.addSummary);



module.exports = router;
