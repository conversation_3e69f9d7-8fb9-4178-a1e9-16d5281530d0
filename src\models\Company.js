const mongoose = require('mongoose');

const CompanySchema = new mongoose.Schema({
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    companyName: {
        type: String,
        required: true,
        trim: true
    },
    companyEmail: {
        type: String,
        required: true,
        unique: true,
        trim: true,
        lowercase: true
    },
    logo: {
        type: String,
        default: null
    },
    website: {
        type: String,
        default: null
    },
    description: {
        type: String,
        maxlength: 1000
    },
    industry: {
        type: String,
        enum: ['Technology', 'Healthcare', 'Finance', 'Education', 'Manufacturing', 'Other'],
        default: 'Technology'
    },
    companySize: {
        type: String,
        enum: ['1-10', '11-50', '51-200', '201-500', '500+'],
        default: '1-10'
    },
    location: {
        address: { type: String },
        city: { type: String },
        state: { type: String },
        country: { type: String },
        pincode: { type: String }
    },
    contactPerson: {
        name: { type: String },
        designation: { type: String },
        phone: { type: String }
    },
    socialLinks: {
        linkedin: { type: String, default: null },
        twitter: { type: String, default: null },
        facebook: { type: String, default: null },
        github: { type: String, default: null },
        website: { type: String, default: null }
    },
    isVerified: {
        type: Boolean,
        default: false
    },
    verificationDocument: {
        type: String,
        default: null
    },
    status: {
        type: String,
        enum: ['pending', 'approved', 'rejected', 'suspended'],
        default: 'pending'
    },
    rejectionReason: {
        type: String,
        default: null
    }
}, {
    timestamps: true
});

module.exports = mongoose.model('Company', CompanySchema);


