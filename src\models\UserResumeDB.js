const mongoose = require("mongoose");

//* ---------- Sub-Schemas ---------- */

const Profile = new mongoose.Schema({
  Network: { type: String, required: true },
  Username: { type: String, required: true },
  ProfileLink: { type: String, required: true },
  ProfileImage: { type: String, required: true },
});
const Experience = new mongoose.Schema({
  Company: { type: String, required: true },
  Position: { type: String, required: true },
  StartDate: { type: Date, required: true },
  EndDate: { type: Date },
  Location: { type: String },
  Website: { type: String },
  Description: { type: String },
});

const Education = new mongoose.Schema({
  Institution: { type: String, required: true },
  Degree: { type: String, required: true },
  StartDate: { type: Date, required: true },
  EndDate: { type: Date },
  Location: { type: String },
});
const Skills = new mongoose.Schema({
  Skill: { type: String, required: true },
  Proficiency: { type: String, required: true },
  Keywords: { type: [String] },
  Description: { type: String },
});

const Languages = new mongoose.Schema({
  Name: { type: String, required: true },
  Proficiency: { type: String, required: true },
  Description: { type: String },
});

const Awards = new mongoose.Schema({
  Title: { type: String, required: true },
  Issuer: { type: String, required: true },
  Date: { type: Date, required: true },
  Description: { type: String },
});

const Certifications = new mongoose.Schema({
  Title: { type: String, required: true },
  Issuer: { type: String, required: true },
  Date: { type: Date, required: true },
  Website: { type: String },
  Description: { type: String },
});

const Interests = new mongoose.Schema({
  Interest: { type: String, required: true },
  Keywords: { type: [String] },
});

const Publications = new mongoose.Schema({
  Title: { type: String, required: true },
  Publisher: { type: String, required: true },
  Date: { type: Date, required: true },
  Website: { type: String },
  Description: { type: String },
});

const Volunteering = new mongoose.Schema({
  Organization: { type: String, required: true },
  Position: { type: String, required: true },
  StartDate: { type: Date, required: true },
  EndDate: { type: Date },
  Location: { type: String },
  Description: { type: String },
});

const References = new mongoose.Schema({
  Name: { type: String, required: true },
  Position: { type: String, required: true },
  Company: { type: String, required: true },
  Email: { type: String, required: true },
  Phone: { type: String },
});

const Projects = new mongoose.Schema({
  Title: { type: String, required: true },
  Description: { type: String },
  Link: { type: String },
  Technologies: { type: [String] },
  StartDate: { type: Date },
  EndDate: { type: Date },
});

//* ---------- Main Resume Schema ---------- */

const ResumeProfileSchema = new mongoose.Schema(
  {
    Title: { type: String, required: true },
    summery: { type: String },
    Email: { type: String, required: true },
    Template: { type: String, default: "default" },
    isPublished: { type: Boolean, default: false },
    PublishURL: { type: String, default: null },
    PublicAccess: { type: Boolean, default: false },
    LastExported: { type: Date, default: null },
    PDFLink: { type: String, default: null },
    ProfilePic: { type: String, required: false },
    ProfileImagePublicId: { type: String },
    Tags: { type: [String], default: [] },
    Order: { type: [String], default: [] },
    Headline: { type: String },
    Website: { type: String },
    Phone: { type: String },
    Location: { type: String },

    Profiles: { type: [Profile], default: [] },
    Experience: { type: [Experience], default: [] },
    Education: { type: [Education], default: [] },
    Skills: { type: [Skills], default: [] },
    Languages: { type: [Languages], default: [] },
    Awards: { type: [Awards], default: [] },
    Certifications: { type: [Certifications], default: [] },
    Interests: { type: [Interests], default: [] },
    Publications: { type: [Publications], default: [] },
    Volunteering: { type: [Volunteering], default: [] },
    References: { type: [References], default: [] },
    Projects: { type: [Projects], default: [] },
  },
  { timestamps: true }
);

//* ---------- Index & Export ---------- */
ResumeProfileSchema.index({ Email: 1, Title: 1 }, { unique: true });

module.exports =
  mongoose.models.ResumeProfile ||
  mongoose.model("ResumeProfile", ResumeProfileSchema);
