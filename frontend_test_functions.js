// Frontend Functions for Test Management with Your Actual Data

// Base API configuration
const API_BASE_URL = 'http://localhost:5000/api/tests';

// Your actual data from the database
const testData = {
  testId: '687f67966fad6a0ea16667e0',
  companyId: '6876052fb509bcbdcd5d8a8e',
  questionId: '687777f10b7ee9e83b238045',
  questionCompanyId: '687753791d871dbfc74073f9'
};

// API Client setup
const apiClient = {
  async request(url, options = {}) {
    const token = localStorage.getItem('authToken'); // Get your auth token
    
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` })
      },
      credentials: 'include',
      ...options
    };

    try {
      const response = await fetch(`${API_BASE_URL}${url}`, config);
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || `HTTP error! status: ${response.status}`);
      }
      
      return data;
    } catch (error) {
      console.error('API Error:', error);
      throw error;
    }
  }
};

// 1. ADD QUESTIONS TO TEST (Fixed for your data)
async function addQuestionsToTest(testId, questionIds, points = 1) {
  try {
    console.log('Adding questions to test:', { testId, questionIds, points });
    
    // Validate input
    if (!testId) {
      throw new Error('Test ID is required');
    }
    
    if (!questionIds || !Array.isArray(questionIds) || questionIds.length === 0) {
      throw new Error('Question IDs array is required and must not be empty');
    }

    const response = await apiClient.request(`/${testId}/questions`, {
      method: 'POST',
      body: JSON.stringify({
        questionIds: questionIds,
        points: points
      })
    });

    console.log('✅ Questions added successfully:', response);
    return response;
  } catch (error) {
    console.error('❌ Error adding questions:', error.message);
    throw error;
  }
}

// 2. GET TEST DETAILS
async function getTestDetails(testId) {
  try {
    const response = await apiClient.request(`/${testId}`);
    console.log('✅ Test details:', response.test);
    return response.test;
  } catch (error) {
    console.error('❌ Error getting test details:', error.message);
    throw error;
  }
}

// 3. GET ALL TESTS
async function getAllTests() {
  try {
    const response = await apiClient.request('/');
    console.log('✅ All tests:', response);
    return response.tests;
  } catch (error) {
    console.error('❌ Error getting tests:', error.message);
    throw error;
  }
}

// 4. GET QUESTIONS BY CATEGORY
async function getQuestionsByCategory(category = 'Frontend') {
  try {
    const response = await apiClient.request(`/questions/by-category?category=${category}`);
    console.log('✅ Questions by category:', response);
    return response.questions;
  } catch (error) {
    console.error('❌ Error getting questions by category:', error.message);
    throw error;
  }
}

// 5. REMOVE QUESTIONS FROM TEST
async function removeQuestionsFromTest(testId, questionIds) {
  try {
    if (!questionIds || !Array.isArray(questionIds) || questionIds.length === 0) {
      throw new Error('Question IDs array is required and must not be empty');
    }

    const response = await apiClient.request(`/${testId}/questions`, {
      method: 'DELETE',
      body: JSON.stringify({
        questionIds: questionIds
      })
    });

    console.log('✅ Questions removed successfully:', response);
    return response;
  } catch (error) {
    console.error('❌ Error removing questions:', error.message);
    throw error;
  }
}

// 6. CREATE TEST
async function createTest(testData) {
  try {
    const response = await apiClient.request('/', {
      method: 'POST',
      body: JSON.stringify(testData)
    });

    console.log('✅ Test created successfully:', response);
    return response.test;
  } catch (error) {
    console.error('❌ Error creating test:', error.message);
    throw error;
  }
}

// 7. UPDATE TEST
async function updateTest(testId, updateData) {
  try {
    const response = await apiClient.request(`/${testId}`, {
      method: 'PUT',
      body: JSON.stringify(updateData)
    });

    console.log('✅ Test updated successfully:', response);
    return response.test;
  } catch (error) {
    console.error('❌ Error updating test:', error.message);
    throw error;
  }
}

// USAGE EXAMPLES WITH YOUR ACTUAL DATA

// Example 1: Add the question to your test
async function addQuestionToYourTest() {
  try {
    const result = await addQuestionsToTest(
      testData.testId,           // '687f67966fad6a0ea16667e0'
      [testData.questionId],     // ['687777f10b7ee9e83b238045']
      5                          // 5 points (since the question has 5 points)
    );
    
    alert(`Success: ${result.message}`);
    return result;
  } catch (error) {
    alert(`Error: ${error.message}`);
    throw error;
  }
}

// Example 2: Get your test details
async function getYourTestDetails() {
  try {
    const test = await getTestDetails(testData.testId);
    console.log('Your test:', test);
    return test;
  } catch (error) {
    alert(`Error: ${error.message}`);
    throw error;
  }
}

// Example 3: Get Frontend questions
async function getFrontendQuestions() {
  try {
    const questions = await getQuestionsByCategory('Frontend');
    console.log('Frontend questions:', questions);
    return questions;
  } catch (error) {
    alert(`Error: ${error.message}`);
    throw error;
  }
}

// React Component Example
const TestManagement = () => {
  const [test, setTest] = React.useState(null);
  const [questions, setQuestions] = React.useState([]);
  const [loading, setLoading] = React.useState(false);

  // Load test details
  const loadTestDetails = async () => {
    setLoading(true);
    try {
      const testDetails = await getTestDetails(testData.testId);
      setTest(testDetails);
    } catch (error) {
      console.error('Error loading test:', error);
    } finally {
      setLoading(false);
    }
  };

  // Load available questions
  const loadQuestions = async () => {
    try {
      const frontendQuestions = await getQuestionsByCategory('Frontend');
      setQuestions(frontendQuestions);
    } catch (error) {
      console.error('Error loading questions:', error);
    }
  };

  // Add selected question to test
  const handleAddQuestion = async (questionId) => {
    try {
      await addQuestionsToTest(testData.testId, [questionId], 5);
      await loadTestDetails(); // Refresh test details
      alert('Question added successfully!');
    } catch (error) {
      alert(`Error: ${error.message}`);
    }
  };

  React.useEffect(() => {
    loadTestDetails();
    loadQuestions();
  }, []);

  if (loading) return <div>Loading...</div>;

  return (
    <div>
      <h2>Test Management</h2>
      
      {test && (
        <div>
          <h3>{test.testName}</h3>
          <p>Description: {test.description}</p>
          <p>Duration: {test.duration} minutes</p>
          <p>Total Points: {test.totalPoints}</p>
          <p>Questions: {test.questions.length}</p>
        </div>
      )}

      <h3>Available Questions</h3>
      {questions.map(question => (
        <div key={question._id} style={{ border: '1px solid #ccc', margin: '10px', padding: '10px' }}>
          <h4>{question.questionText}</h4>
          <p>Type: {question.questionType}</p>
          <p>Difficulty: {question.difficulty}</p>
          <p>Points: {question.points}</p>
          <button onClick={() => handleAddQuestion(question._id)}>
            Add to Test
          </button>
        </div>
      ))}
    </div>
  );
};

// Manual Testing Functions
console.log(`
🧪 MANUAL TESTING INSTRUCTIONS:

1. Open browser console
2. Set your auth token: localStorage.setItem('authToken', 'YOUR_TOKEN_HERE')
3. Run these functions:

// Add question to test
addQuestionToYourTest()

// Get test details
getYourTestDetails()

// Get frontend questions
getFrontendQuestions()

// Add multiple questions
addQuestionsToTest('${testData.testId}', ['${testData.questionId}', 'another-question-id'], 3)

Your test ID: ${testData.testId}
Your question ID: ${testData.questionId}
`);

// Export functions for use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    addQuestionsToTest,
    getTestDetails,
    getAllTests,
    getQuestionsByCategory,
    removeQuestionsFromTest,
    createTest,
    updateTest,
    addQuestionToYourTest,
    getYourTestDetails,
    getFrontendQuestions
  };
}
