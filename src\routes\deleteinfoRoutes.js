const express = require('express');
const router = express.Router();
const authMiddleware = require('../middlewares/authMiddleware');
const controller = require('../controllers/deleteinfoController');

router.get('/', controller.testAPI);
router.delete('/website', authMiddleware, controller.deleteWebsite);
router.delete('/phone', authMiddleware, controller.deletePhone);
router.delete('/location', authMiddleware, controller.deleteLocation);


router.delete('/deleteprofile/:id', authMiddleware, controller.deleteProfile);
router.delete('/deleteexperience/:id', authMiddleware, controller.deleteExperience);
router.delete('/deleteeducation/:id', authMiddleware, controller.deleteEducation);
router.delete('/deleteskills/:id', authMiddleware, controller.deleteSkill);
router.delete('/deletelanguages/:id', authMiddleware, controller.deleteLanguage);
router.delete('/deleteawards/:id', authMiddleware, controller.deleteAward);
router.delete('/deletecertifications/:id', authMiddleware, controller.deleteCertification);
router.delete('/deleteinterests/:id', authMiddleware, controller.deleteInterest);
router.delete('/deletepublications/:id', authMiddleware, controller.deletePublication);
router.delete('/deletevolunteering/:id', authMiddleware, controller.deleteVolunteering);
router.delete('/deletereferences/:id', authMiddleware, controller.deleteReference);
router.delete('/deleteproject/:id', authMiddleware, controller.deleteProject);
// router.delete('/awards/:id', authMiddleware, controller.deleteProject);



module.exports = router;




