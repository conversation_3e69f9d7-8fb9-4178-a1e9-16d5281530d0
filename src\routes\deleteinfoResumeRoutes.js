const express = require('express');
const router = express.Router();
const authMiddleware = require('../middlewares/authMiddleware');
const controller = require('../controllers/deleteinfoResumeController');

router.get('/', controller.testAPI);

router.delete('/:resumeId/deleteprofile/:id', authMiddleware, controller.deleteProfile);
router.delete('/:resumeId/deleteexperience/:id', authMiddleware, controller.deleteExperience);
router.delete('/:resumeId/deleteeducation/:id', authMiddleware, controller.deleteEducation);
router.delete('/:resumeId/deleteskills/:id', authMiddleware, controller.deleteSkill);
router.delete('/:resumeId/deletelanguages/:id', authMiddleware, controller.deleteLanguage);
router.delete('/:resumeId/deleteawards/:id', authMiddleware, controller.deleteAward);
router.delete('/:resumeId/deletecertifications/:id', authMiddleware, controller.deleteCertification);
router.delete('/:resumeId/deleteinterests/:id', authMiddleware, controller.deleteInterest);
router.delete('/:resumeId/deletepublications/:id', authMiddleware, controller.deletePublication);
router.delete('/:resumeId/deletevolunteering/:id', authMiddleware, controller.deleteVolunteering);
router.delete('/:resumeId/deletereferences/:id', authMiddleware, controller.deleteReference);
router.delete('/:resumeId/deletesummary', authMiddleware, controller.deleteSummary);
router.delete('/:resumeId/deleteprojects/:id', authMiddleware, controller.deleteProject);


module.exports = router;
