# Company Candidate Management API

This document describes the enhanced API endpoints that allow companies to view and filter job applicants based on their resume data.

## Authentication

All endpoints require company authentication. Include the JWT token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

The user must have the role `company` to access these endpoints.

## Endpoints

### 1. Get Job Applications with Resume Data

**GET** `/api/company/jobs/:jobId/applications-with-resumes`

Retrieves all applications for a specific job with complete resume data and filtering capabilities.

#### Parameters

- `jobId` (path parameter): The ID of the job

#### Query Parameters

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `page` | number | Page number for pagination | `1` |
| `limit` | number | Number of results per page | `10` |
| `status` | string | Filter by application status | `applied`, `shortlisted`, `rejected` |
| `sortBy` | string | Sort field | `appliedAt`, `name`, `experience` |
| `sortOrder` | string | Sort direction | `asc`, `desc` |
| `skills` | string | Comma-separated skills to filter by | `JavaScript,React,Node.js` |
| `experienceLevel` | string | Experience level filter | `entry`, `mid`, `senior`, `lead` |
| `location` | string | Location filter | `New York` |
| `education` | string | Education filter | `Computer Science` |
| `minExperience` | number | Minimum years of experience | `2` |
| `maxExperience` | number | Maximum years of experience | `5` |
| `languages` | string | Comma-separated languages | `English,Spanish` |
| `certifications` | string | Certification filter | `AWS` |
| `searchTerm` | string | Search across multiple fields | `developer` |

#### Example Request

```bash
GET /api/company/jobs/64a1b2c3d4e5f6789012345/applications-with-resumes?skills=JavaScript,React&experienceLevel=mid&page=1&limit=10
```

#### Example Response

```json
{
  "success": true,
  "applications": [
    {
      "jobId": "64a1b2c3d4e5f6789012345",
      "jobTitle": "Frontend Developer",
      "jobCategory": "Frontend",
      "jobLocation": "New York",
      "application": {
        "candidateId": "64a1b2c3d4e5f6789012346",
        "appliedAt": "2024-01-15T10:30:00.000Z",
        "status": "applied",
        "testScore": null
      },
      "candidate": {
        "name": "John Doe",
        "email": "<EMAIL>",
        "userId": "64a1b2c3d4e5f6789012346"
      },
      "resume": {
        "Title": "Senior Frontend Developer",
        "summery": "Experienced developer with 5+ years...",
        "Location": "New York",
        "Skills": [
          {
            "Skill": "JavaScript",
            "Proficiency": "Expert"
          },
          {
            "Skill": "React",
            "Proficiency": "Advanced"
          }
        ],
        "Experience": [...],
        "Education": [...],
        // ... other resume fields
      },
      "totalExperience": 5.2
    }
  ],
  "pagination": {
    "current": 1,
    "pages": 3,
    "total": 25
  },
  "filters": {
    "status": "all",
    "skills": "JavaScript,React",
    "experienceLevel": "mid",
    // ... other applied filters
  }
}
```

### 2. Get All Applications Across Jobs

**GET** `/api/company/applications-with-resumes`

Retrieves all applications across all company jobs with resume data.

#### Query Parameters

Same as above, plus:

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `jobId` | string | Filter by specific job ID | `64a1b2c3d4e5f6789012345` |

#### Example Request

```bash
GET /api/company/applications-with-resumes?skills=Python&location=San Francisco&page=1&limit=20
```

### 3. Get Detailed Candidate Information

**GET** `/api/company/jobs/:jobId/candidates/:candidateId`

Retrieves detailed information about a specific candidate including complete resume data and test results.

#### Parameters

- `jobId` (path parameter): The ID of the job
- `candidateId` (path parameter): The ID of the candidate

#### Example Request

```bash
GET /api/company/jobs/64a1b2c3d4e5f6789012345/candidates/64a1b2c3d4e5f6789012346
```

#### Example Response

```json
{
  "success": true,
  "data": {
    "job": {
      "id": "64a1b2c3d4e5f6789012345",
      "title": "Frontend Developer",
      "category": "Frontend",
      "location": "New York",
      "experienceLevel": "Mid",
      "hasTest": true
    },
    "application": {
      "appliedAt": "2024-01-15T10:30:00.000Z",
      "status": "applied",
      "testScore": null
    },
    "candidate": {
      "id": "64a1b2c3d4e5f6789012346",
      "name": "John Doe",
      "email": "<EMAIL>",
      "totalExperience": 5.2
    },
    "resume": {
      // Complete resume data
    },
    "testResults": {
      "testTitle": "Frontend Skills Assessment",
      "status": "completed",
      "score": 85,
      "totalScore": 100,
      "percentage": 85,
      "startedAt": "2024-01-16T09:00:00.000Z",
      "submittedAt": "2024-01-16T10:30:00.000Z",
      "feedback": "Good performance overall"
    }
  }
}
```

### 4. Get Candidate Analytics

**GET** `/api/company/candidates/analytics/:jobId?`

Retrieves analytics and statistics about candidates for filtering insights.

#### Parameters

- `jobId` (optional path parameter): Specific job ID for analytics, omit for all jobs

#### Example Request

```bash
GET /api/company/candidates/analytics/64a1b2c3d4e5f6789012345
```

#### Example Response

```json
{
  "success": true,
  "analytics": {
    "totalApplications": 150,
    "statusBreakdown": {
      "applied": 80,
      "shortlisted": 45,
      "rejected": 20,
      "hired": 5
    },
    "topSkills": [
      { "name": "JavaScript", "count": 120 },
      { "name": "React", "count": 95 },
      { "name": "Python", "count": 75 }
    ],
    "topLocations": [
      { "name": "New York", "count": 60 },
      { "name": "San Francisco", "count": 45 },
      { "name": "Remote", "count": 30 }
    ],
    "topEducationLevels": [
      { "name": "Bachelor of Computer Science", "count": 80 },
      { "name": "Master of Computer Science", "count": 35 }
    ],
    "topLanguages": [
      { "name": "English", "count": 140 },
      { "name": "Spanish", "count": 25 }
    ],
    "topCertifications": [
      { "name": "AWS Certified Developer", "count": 30 },
      { "name": "Google Cloud Professional", "count": 20 }
    ]
  }
}
```

## Error Responses

All endpoints return consistent error responses:

```json
{
  "error": "Error message description"
}
```

Common HTTP status codes:
- `400`: Bad Request (invalid parameters)
- `401`: Unauthorized (missing or invalid token)
- `403`: Forbidden (insufficient permissions)
- `404`: Not Found (job or candidate not found)
- `500`: Internal Server Error

## Usage Examples

### Filter by Multiple Criteria

```bash
GET /api/company/jobs/64a1b2c3d4e5f6789012345/applications-with-resumes?skills=JavaScript,React&location=New York&minExperience=2&maxExperience=5&sortBy=experience&sortOrder=desc
```

### Search for Specific Candidates

```bash
GET /api/company/applications-with-resumes?searchTerm=senior developer&experienceLevel=senior
```

### Get Analytics for Decision Making

```bash
GET /api/company/candidates/analytics
```

## Notes

- All date fields are returned in ISO 8601 format
- Experience is calculated in years with decimal precision
- Skills, languages, and other array fields support partial matching
- Search terms are case-insensitive
- Pagination is zero-indexed (page 1 is the first page)
- Maximum limit per page is typically 100 (configurable)

## Data Relationships

The API connects three main data sources:
1. **Job Applications**: Stored in `Job.applicants` array
2. **User Data**: Basic candidate information from `User` collection
3. **Resume Data**: Complete resume information from `ResumeProfile` collection

The connection is made through the candidate's email address, linking `User.email` to `ResumeProfile.Email`.
