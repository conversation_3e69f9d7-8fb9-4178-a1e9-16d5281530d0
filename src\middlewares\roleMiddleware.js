module.exports = function (allowedRoles = []) {
    return function (req, res, next) {
        try {
            const userRole = req.user.role;

            if (!userRole) {
                return res.status(403).json({ error: 'Access denied. No role found.' });
            }

            if (!allowedRoles.includes(userRole)) {
                return res.status(403).json({ error: 'Access denied. Insufficient role permissions.' });
            }

            next();
        } catch (err) {
            return res.status(500).json({ error: 'Role check failed', details: err.message });
        }
    };
};