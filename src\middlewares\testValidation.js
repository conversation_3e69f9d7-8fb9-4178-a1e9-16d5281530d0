const mongoose = require('mongoose');
const Joi = require('joi');

// Validation schemas
const createTestSchema = Joi.object({
    testName: Joi.string().trim().min(3).max(200).required(),
    description: Joi.string().trim().max(500).allow(''),
    duration: Joi.number().integer().min(5).max(300).required(),
    passingScore: Joi.number().integer().min(0).max(100).default(50),
    scheduledDate: Joi.date().iso().required(),
    endDate: Joi.date().iso().greater(Joi.ref('scheduledDate')).required(),
    questions: Joi.array().items(
        Joi.object({
            questionId: Joi.string().custom((value, helpers) => {
                if (!mongoose.Types.ObjectId.isValid(value)) {
                    return helpers.error('any.invalid');
                }
                return value;
            }).required(),
            points: Joi.number().integer().min(1).max(10).default(1)
        })
    ).min(1).required(),
    associatedJobs: Joi.array().items(
        Joi.string().custom((value, helpers) => {
            if (!mongoose.Types.ObjectId.isValid(value)) {
                return helpers.error('any.invalid');
            }
            return value;
        })
    ).default([]),
    instructions: Joi.string().trim().max(1000).allow(''),
    allowedAttempts: Joi.number().integer().min(1).max(5).default(1),
    randomizeQuestions: Joi.boolean().default(false),
    showResults: Joi.boolean().default(false)
});

const updateTestSchema = Joi.object({
    testName: Joi.string().trim().min(3).max(200),
    description: Joi.string().trim().max(500).allow(''),
    duration: Joi.number().integer().min(5).max(300),
    passingScore: Joi.number().integer().min(0).max(100),
    scheduledDate: Joi.date().iso(),
    endDate: Joi.date().iso(),
    instructions: Joi.string().trim().max(1000).allow(''),
    allowedAttempts: Joi.number().integer().min(1).max(5),
    randomizeQuestions: Joi.boolean(),
    showResults: Joi.boolean()
}).min(1);

const assignCandidatesSchema = Joi.object({
    candidateIds: Joi.array().items(
        Joi.string().custom((value, helpers) => {
            if (!mongoose.Types.ObjectId.isValid(value)) {
                return helpers.error('any.invalid');
            }
            return value;
        })
    ).min(1).max(100).required()
});

const questionBundleSchema = Joi.object({
    bundleName: Joi.string().trim().min(3).max(200).required(),
    description: Joi.string().trim().max(500).allow(''),
    category: Joi.string().valid(
        'Frontend', 'Backend', 'Full Stack', 'Data Science', 
        'DevOps', 'Mobile', 'UI/UX', 'QA', 'Aptitude', 'Logical', 'Other'
    ).required(),
    difficulty: Joi.string().valid('Easy', 'Medium', 'Hard', 'Mixed').default('Mixed'),
    questionIds: Joi.array().items(
        Joi.string().custom((value, helpers) => {
            if (!mongoose.Types.ObjectId.isValid(value)) {
                return helpers.error('any.invalid');
            }
            return value;
        })
    ).min(1).max(50).required(),
    tags: Joi.array().items(Joi.string().trim().max(50)).max(10).default([])
});

const filterQuestionsSchema = Joi.object({
    searchTerm: Joi.string().trim().max(100).allow(''),
    category: Joi.string().valid(
        'Frontend', 'Backend', 'Full Stack', 'Data Science', 
        'DevOps', 'Mobile', 'UI/UX', 'QA', 'Aptitude', 'Logical', 'Other'
    ),
    difficulty: Joi.string().valid('Easy', 'Medium', 'Hard'),
    type: Joi.string().valid('MCQ', 'Multiple-Select', 'Short-Answer', 'Code'),
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(50).default(20)
});

const searchCandidatesSchema = Joi.object({
    search: Joi.string().trim().max(100).allow(''),
    experience: Joi.string().trim().max(50).allow(''),
    skills: Joi.string().trim().max(200).allow(''),
    location: Joi.string().trim().max(100).allow(''),
    status: Joi.string().valid('all', 'applied', 'test_pending', 'test_completed', 'shortlisted', 'rejected').default('all'),
    jobId: Joi.string().custom((value, helpers) => {
        if (value && !mongoose.Types.ObjectId.isValid(value)) {
            return helpers.error('any.invalid');
        }
        return value;
    }).allow(''),
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(50).default(10)
});

// Validation middleware functions
const validateCreateTest = (req, res, next) => {
    const { error, value } = createTestSchema.validate(req.body, { 
        abortEarly: false,
        stripUnknown: true 
    });

    if (error) {
        const errors = error.details.map(detail => ({
            field: detail.path.join('.'),
            message: detail.message
        }));
        return res.status(400).json({
            error: 'Validation failed',
            details: errors
        });
    }

    req.body = value;
    next();
};

const validateUpdateTest = (req, res, next) => {
    const { error, value } = updateTestSchema.validate(req.body, { 
        abortEarly: false,
        stripUnknown: true 
    });

    if (error) {
        const errors = error.details.map(detail => ({
            field: detail.path.join('.'),
            message: detail.message
        }));
        return res.status(400).json({
            error: 'Validation failed',
            details: errors
        });
    }

    req.body = value;
    next();
};

const validateAssignCandidates = (req, res, next) => {
    const { error, value } = assignCandidatesSchema.validate(req.body, { 
        abortEarly: false,
        stripUnknown: true 
    });

    if (error) {
        const errors = error.details.map(detail => ({
            field: detail.path.join('.'),
            message: detail.message
        }));
        return res.status(400).json({
            error: 'Validation failed',
            details: errors
        });
    }

    req.body = value;
    next();
};

const validateQuestionBundle = (req, res, next) => {
    const { error, value } = questionBundleSchema.validate(req.body, { 
        abortEarly: false,
        stripUnknown: true 
    });

    if (error) {
        const errors = error.details.map(detail => ({
            field: detail.path.join('.'),
            message: detail.message
        }));
        return res.status(400).json({
            error: 'Validation failed',
            details: errors
        });
    }

    req.body = value;
    next();
};

const validateFilterQuestions = (req, res, next) => {
    const { error, value } = filterQuestionsSchema.validate(req.query, { 
        abortEarly: false,
        stripUnknown: true 
    });

    if (error) {
        const errors = error.details.map(detail => ({
            field: detail.path.join('.'),
            message: detail.message
        }));
        return res.status(400).json({
            error: 'Validation failed',
            details: errors
        });
    }

    req.query = value;
    next();
};

const validateSearchCandidates = (req, res, next) => {
    const { error, value } = searchCandidatesSchema.validate(req.query, { 
        abortEarly: false,
        stripUnknown: true 
    });

    if (error) {
        const errors = error.details.map(detail => ({
            field: detail.path.join('.'),
            message: detail.message
        }));
        return res.status(400).json({
            error: 'Validation failed',
            details: errors
        });
    }

    req.query = value;
    next();
};

// Parameter validation
const validateObjectId = (paramName) => {
    return (req, res, next) => {
        const id = req.params[paramName];
        if (!mongoose.Types.ObjectId.isValid(id)) {
            return res.status(400).json({
                error: `Invalid ${paramName}`,
                message: `The provided ${paramName} is not a valid ObjectId`
            });
        }
        next();
    };
};

module.exports = {
    validateCreateTest,
    validateUpdateTest,
    validateAssignCandidates,
    validateQuestionBundle,
    validateFilterQuestions,
    validateSearchCandidates,
    validateObjectId
};
