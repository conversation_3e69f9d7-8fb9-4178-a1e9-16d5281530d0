const express = require('express');
const router = express.Router();
const {
    createResume,
    getUserResumes,
    getResumeById,
    updateResume,
    deleteResume,
    duplicateResume,
} = require('../controllers/resumeController.js');
const authMiddleware = require('../middlewares/authMiddleware'); // corrected path

router.use(authMiddleware);
router.post('/', createResume);
router.get('/', getUserResumes);
router.get('/:id', getResumeById);
router.put('/:id', updateResume);
router.delete('/:id', deleteResume);
router.post('/:id/duplicate', duplicateResume);
module.exports = router;
