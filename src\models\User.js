const mongoose = require('mongoose');

const UserSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true
    },
    email: {
      type: String,
      unique: true,
      required: true
    },
    password: {
      type: String,
      required: true
    },
    role: {
      type: String,
      enum: ['candidate', 'interviewer', 'admin', 'company'],
      default: 'candidate'
    },
    isVerified: {
      type: Boolean,
      default: false
    },
    otp: { type: String },
    otpExpiry: { type: Date },
    created_at: { type: Date, default: Date.now }
  },
  { timestamps: true }
);

module.exports = mongoose.model('User', UserSchema);
