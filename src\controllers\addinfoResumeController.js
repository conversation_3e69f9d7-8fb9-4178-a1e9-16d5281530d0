const ResumeProfile = require('../models/UserResumeDB');
const cloudinary = require('../config/cloudinary');

const addToResumeField = async (req, res, fieldName) => {
  const { id } = req.params;
  const dataToInsert = req.body;

  try {
    const updatedResume = await ResumeProfile.findByIdAndUpdate(
      id,
      { $push: { [fieldName]: dataToInsert } },
      { new: true, runValidators: true }
    );

    if (!updatedResume) {
      return res.status(404).json({ error: 'Resume not found' });
    }

    res.status(200).json({
      message: `${fieldName} added successfully`,
      updated: updatedResume,
    });
  } catch (err) {
    res.status(500).json({ error: 'Failed to update resume', details: err.message });
  }
};

const testAPI = (req, res) => {
  res.status(200).send('Add Resume API is working');
};

const addSummary = async (req, res) => {
  const { id } = req.params;
  const { summary } = req.body;

  if (!summary || typeof summary !== 'string') {
    return res.status(400).json({ error: 'Summary is required and must be a string.' });
  }

  try {
    const resume = await ResumeProfile.findById(id);
    if (!resume) {
      return res.status(404).json({ error: 'Resume not found.' });
    }

    resume.summery = summary; // NOTE: If typo, fix schema field to `summary`
    await resume.save();

    res.status(200).json({ message: 'Summary added successfully.', resume });
  } catch (error) {
    console.error('Error adding summary:', error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
};

const uploadImage = async (req, res) => {
  const { id } = req.params;
  const file = req.file;

  if (!file) {
    return res.status(400).json({ error: "No image file uploaded" });
  }

  try {
    // Find resume by ID
    const resume = await ResumeProfile.findById(id);
    if (!resume) {
      return res.status(404).json({ error: "Resume not found" });
    }

    // If previous image exists, delete it from Cloudinary
    if (resume.ProfileImagePublicId) {
      await cloudinary.uploader.destroy(resume.ProfileImagePublicId);
    }
    resume.ProfilePic = file.path; // Cloudinary URL
    resume.ProfileImagePublicId = file.filename; // Cloudinary public ID

    await resume.save({ validateBeforeSave: false });

    res.status(200).json({
      message: "Image uploaded successfully",
      imageUrl: file.path,
      publicId: file.filename,
    });
  } catch (err) {
    console.error("Image upload error:", err);
    res.status(500).json({ error: "Failed to upload image" });
  }
};

const deleteImage = async (req, res) => {
  const { id } = req.params;

  try {
    const resume = await ResumeProfile.findById(id);
    if (!resume || !resume.ProfileImagePublicId) {
      return res.status(404).json({ error: 'Profile image not found' });
    }

    await cloudinary.uploader.destroy(resume.ProfileImagePublicId);

    resume.ProfileImage = '';
    resume.ProfileImagePublicId = '';

    await resume.save({ validateBeforeSave: false });

    res.status(200).json({ message: 'Profile image deleted successfully' });
  } catch (err) {
    console.error('Image delete error:', err);
    res.status(500).json({ error: 'Failed to delete profile image' });
  }
};

const updateBasicsField = async (req, res) => {
  const { id } = req.params;
  const { key, value } = req.body;

  // Allow only specific fields to be updated
  const allowedFields = ["Headline", "Website", "Phone", "Location", "ProfilePic"];
  if (!allowedFields.includes(key)) {
    return res.status(400).json({ error: "Invalid field name" });
  }

  try {
    const resume = await ResumeProfile.findById(id);
    if (!resume) {
      return res.status(404).json({ error: "Resume not found" });
    }

    resume[key] = value;
    await resume.save();

    res.status(200).json({ message: `${key} updated`, resume });
  } catch (err) {
    res.status(500).json({ error: "Failed to update", details: err.message });
  }
};
const addVolunteering = async (req, res) => {
  const resumeId = req.params.id;
  const { Organization, Position, StartDate, EndDate, Location, Description } = req.body;

  if (!Organization || !Position) {
    return res.status(400).json({ error: "Organization and Position are required" });
  }

  const newVolunteering = { Organization, Position, StartDate, EndDate, Location, Description };

  try {
    const updatedResume = await ResumeProfile.findByIdAndUpdate(
      resumeId,
      { $push: { Volunteering: newVolunteering } }, // ⚠️ Ensure this matches the field in the schema (capital 'V')
      { new: true }
    );

    if (!updatedResume) {
      return res.status(404).json({ error: "Resume not found" });
    }

    res.json({ message: "Volunteering entry added", resume: updatedResume });
  } catch (err) {
    console.error("Add Volunteering Error:", err);
    res.status(500).json({ error: "Failed to add volunteering entry" });
  }
};


module.exports = {
  testAPI,
  addProfile: (req, res) => addToResumeField(req, res, 'Profiles'),
  addExperience: (req, res) => addToResumeField(req, res, 'Experience'),
  addEducation: (req, res) => addToResumeField(req, res, 'Education'),
  addSkill: (req, res) => addToResumeField(req, res, 'Skills'),
  addLanguage: (req, res) => addToResumeField(req, res, 'Languages'),
  addAward: (req, res) => addToResumeField(req, res, 'Awards'),
  addCertification: (req, res) => addToResumeField(req, res, 'Certifications'),
  addInterest: (req, res) => addToResumeField(req, res, 'Interests'),
  addPublication: (req, res) => addToResumeField(req, res, 'Publications'),
  // addVolunteering: (req, res) => addToResumeField(req, res, 'Volunteering'),
  addReference: (req, res) => addToResumeField(req, res, 'References'),
  addProject: (req, res) => addToResumeField(req, res, 'Projects'),
  addSummary,
  uploadImage,
  deleteImage,
  updateBasicsField,
  addVolunteering
};
