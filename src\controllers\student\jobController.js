const Job = require('../../models/Job');
const User = require('../../models/User');
const mongoose = require('mongoose');

// 1. GET ALL JOBS
const getAllJobs = async (req, res) => {
    try {
        const {
            tech,
            category,
            jobType,
            experienceLevel,
            workMode,
            location,
            salaryMin,
            salaryMax,
            page = 1,
            limit = 10,
            sortBy = 'createdAt',
            sortOrder = 'desc'
        } = req.query;

        // Build filter object
        let filter = { isActive: true };

        // Add application deadline filter (only show jobs with future deadlines)
        filter.applicationDeadline = { $gte: new Date() };

        // Apply filters
        if (tech) {
            filter.techStack = { $in: tech.split(',').map(t => new RegExp(t.trim(), 'i')) };
        }

        if (category) {
            filter.category = category;
        }

        if (jobType) {
            filter.jobType = jobType;
        }

        if (experienceLevel) {
            filter.experienceLevel = experienceLevel;
        }

        if (workMode) {
            filter.workMode = workMode;
        }

        if (location) {
            filter.location = { $regex: location, $options: 'i' };
        }

        // Salary range filter
        if (salaryMin || salaryMax) {
            filter['salary.min'] = {};
            if (salaryMin) filter['salary.min'].$gte = parseInt(salaryMin);
            if (salaryMax) filter['salary.max'] = { $lte: parseInt(salaryMax) };
        }

        // Pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);

        // Sort options
        const sortOptions = {};
        sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

        // Execute query with population
        const jobs = await Job.find(filter)
            .populate('companyId', 'name logo website location')
            .sort(sortOptions)
            .skip(skip)
            .limit(parseInt(limit))
            .select('-applicants'); // Exclude applicants array for privacy

        // Get total count for pagination
        const totalJobs = await Job.countDocuments(filter);

        // Calculate pagination info
        const totalPages = Math.ceil(totalJobs / parseInt(limit));
        const hasNextPage = parseInt(page) < totalPages;
        const hasPrevPage = parseInt(page) > 1;

        res.json({
            success: true,
            data: {
                jobs,
                pagination: {
                    currentPage: parseInt(page),
                    totalPages,
                    totalJobs,
                    hasNextPage,
                    hasPrevPage,
                    limit: parseInt(limit)
                }
            }
        });

    } catch (error) {
        console.error('Error fetching jobs:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching jobs',
            error: error.message
        });
    }
};

// 2. GET JOB BY ID
const getJobById = async (req, res) => {
    try {
        const { jobId } = req.params;
        const studentId = req.user.id;

        // Validate ObjectId
        if (!mongoose.Types.ObjectId.isValid(jobId)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid job ID'
            });
        }

        // Find job with company details
        const job = await Job.findById(jobId)
            .populate('companyId', 'name logo website location description')
            .populate('testId', 'title duration questionCount');

        if (!job) {
            return res.status(404).json({
                success: false,
                message: 'Job not found'
            });
        }

        // Check if job is active and deadline hasn't passed
        if (!job.isActive || job.applicationDeadline < new Date()) {
            return res.status(400).json({
                success: false,
                message: 'This job is no longer accepting applications'
            });
        }

        // Check if student has already applied
        const hasApplied = job.applicants.some(
            applicant => applicant.candidateId.toString() === studentId
        );

        // Get application status if applied
        let applicationStatus = null;
        if (hasApplied) {
            const application = job.applicants.find(
                applicant => applicant.candidateId.toString() === studentId
            );
            applicationStatus = {
                status: application.status,
                appliedAt: application.appliedAt,
                testScore: application.testScore
            };
        }

        // Remove sensitive applicant data
        const jobData = job.toObject();
        delete jobData.applicants;

        res.json({
            success: true,
            data: {
                job: jobData,
                hasApplied,
                applicationStatus,
                canApply: !hasApplied && job.currentApplications < job.maxApplications
            }
        });

    } catch (error) {
        console.error('Error fetching job details:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching job details',
            error: error.message
        });
    }
};

// 3. APPLY TO JOB
const applyToJob = async (req, res) => {
    try {
        const { jobId } = req.params;
        const studentId = req.user.id;
        // Validate ObjectId
        if (!mongoose.Types.ObjectId.isValid(jobId)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid job ID'
            });
        }
        // Check if user is a candidate
        const user = await User.findById(studentId);
        if (!user || user.role !== 'candidate') {
            return res.status(403).json({
                success: false,
                message: 'Only candidates can apply to jobs'
            });
        }
        // Find the job
        const job = await Job.findById(jobId);

        if (!job) {
            return res.status(404).json({
                success: false,
                message: 'Job not found'
            });
        }

        // Check if job is active
        if (!job.isActive) {
            return res.status(400).json({
                success: false,
                message: 'This job is no longer active'
            });
        }

        // Check application deadline
        if (job.applicationDeadline < new Date()) {
            return res.status(400).json({
                success: false,
                message: 'Application deadline has passed'
            });
        }

        // Check if maximum applications reached
        if (job.currentApplications >= job.maxApplications) {
            return res.status(400).json({
                success: false,
                message: 'Maximum number of applications reached for this job'
            });
        }

        // Check if student has already applied
        const hasApplied = job.applicants.some(
            applicant => applicant.candidateId.toString() === studentId
        );

        if (hasApplied) {
            return res.status(400).json({
                success: false,
                message: 'You have already applied to this job'
            });
        }

        // Determine initial status based on whether job has test
        const initialStatus = job.hasTest ? 'test_pending' : 'applied';

        // Add application to job
        job.applicants.push({
            candidateId: studentId,
            appliedAt: new Date(),
            status: initialStatus
        });

        // Increment application count
        job.currentApplications += 1;

        await job.save();

        res.json({
            success: true,
            message: 'Application submitted successfully',
            data: {
                jobId: job._id,
                jobTitle: job.title,
                appliedAt: new Date(),
                status: initialStatus,
                hasTest: job.hasTest,
                testId: job.testId
            }
        });

    } catch (error) {
        console.error('Error applying to job:', error);
        res.status(500).json({
            success: false,
            message: 'Error applying to job',
            error: error.message
        });
    }
};

// 4. GET MY APPLICATIONS
const getMyApplications = async (req, res) => {
    try {
        const studentId = req.user.id;
        const {
            status,
            page = 1,
            limit = 10,
            sortBy = 'appliedAt',
            sortOrder = 'desc'
        } = req.query;

        // Build aggregation pipeline
        const pipeline = [
            // Match jobs where student has applied
            {
                $match: {
                    'applicants.candidateId': new mongoose.Types.ObjectId(studentId)
                }
            },
            // Unwind applicants array
            {
                $unwind: '$applicants'
            },
            // Match only this student's applications
            {
                $match: {
                    'applicants.candidateId': new mongoose.Types.ObjectId(studentId)
                }
            }
        ];

        // Add status filter if provided
        if (status) {
            pipeline.push({
                $match: {
                    'applicants.status': status
                }
            });
        }

        // Add company population
        pipeline.push({
            $lookup: {
                from: 'companies',
                localField: 'companyId',
                foreignField: '_id',
                as: 'company'
            }
        });

        // Add test population if exists
        pipeline.push({
            $lookup: {
                from: 'tests',
                localField: 'testId',
                foreignField: '_id',
                as: 'test'
            }
        });

        // Project required fields
        pipeline.push({
            $project: {
                _id: 1,
                title: 1,
                description: 1,
                category: 1,
                jobType: 1,
                experienceLevel: 1,
                location: 1,
                workMode: 1,
                salary: 1,
                applicationDeadline: 1,
                hasTest: 1,
                company: { $arrayElemAt: ['$company', 0] },
                test: { $arrayElemAt: ['$test', 0] },
                application: {
                    appliedAt: '$applicants.appliedAt',
                    status: '$applicants.status',
                    testScore: '$applicants.testScore'
                }
            }
        });

        // Sort
        const sortOptions = {};
        sortOptions[`application.${sortBy}`] = sortOrder === 'desc' ? -1 : 1;
        pipeline.push({ $sort: sortOptions });

        // Get total count
        const totalPipeline = [...pipeline, { $count: 'total' }];
        const totalResult = await Job.aggregate(totalPipeline);
        const totalApplications = totalResult[0]?.total || 0;

        // Add pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);
        pipeline.push({ $skip: skip });
        pipeline.push({ $limit: parseInt(limit) });

        // Execute aggregation
        const applications = await Job.aggregate(pipeline);

        // Calculate pagination info
        const totalPages = Math.ceil(totalApplications / parseInt(limit));
        const hasNextPage = parseInt(page) < totalPages;
        const hasPrevPage = parseInt(page) > 1;

        // Group applications by status for summary
        const statusSummary = await Job.aggregate([
            {
                $match: {
                    'applicants.candidateId': new mongoose.Types.ObjectId(studentId)
                }
            },
            {
                $unwind: '$applicants'
            },
            {
                $match: {
                    'applicants.candidateId': new mongoose.Types.ObjectId(studentId)
                }
            },
            {
                $group: {
                    _id: '$applicants.status',
                    count: { $sum: 1 }
                }
            }
        ]);

        res.json({
            success: true,
            data: {
                applications,
                pagination: {
                    currentPage: parseInt(page),
                    totalPages,
                    totalApplications,
                    hasNextPage,
                    hasPrevPage,
                    limit: parseInt(limit)
                },
                summary: {
                    total: totalApplications,
                    byStatus: statusSummary.reduce((acc, item) => {
                        acc[item._id] = item.count;
                        return acc;
                    }, {})
                }
            }
        });

    } catch (error) {
        console.error('Error fetching applications:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching applications',
            error: error.message
        });
    }
};

module.exports = {
    getAllJobs,
    getJobById,
    applyToJob,
    getMyApplications
};