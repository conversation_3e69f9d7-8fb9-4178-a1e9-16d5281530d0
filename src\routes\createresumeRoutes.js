
const express = require('express'); const createresumeRoutes = express.Router();
const authMiddleware = require('../middlewares/authMiddleware');
const resumeinfoController = require('../controllers/resumeinfoController');

createresumeRoutes.get('/', (req, res) => {
  res.send('Create Resume Routes Works');
});
createresumeRoutes.post('/create', authMiddleware, resumeinfoController.createResume);
createresumeRoutes.patch('/updatetitle', authMiddleware, resumeinfoController.updateTitle);
createresumeRoutes.patch('/updatetemplate', authMiddleware, resumeinfoController.updateTemplate);
createresumeRoutes.patch('/updatepublication', authMiddleware, resumeinfoController.togglePublication);
createresumeRoutes.get('/publish-url/:resumeId', authMiddleware, resumeinfoController.getPublishURL);
createresumeRoutes.patch('/updatepublicaccess', authMiddleware, resumeinfoController.togglePublicAccess);
createresumeRoutes.patch('/updatepdf-link', authMiddleware, resumeinfoController.updatePDFLink);
module.exports = createresumeRoutes;

