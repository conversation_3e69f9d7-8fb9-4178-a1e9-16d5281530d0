const ResumeProfile = require('../models/userResumeDB');

const createResume = async (req, res) => {
  try {
    const { Title } = req.body;
    const email = req.user.email;
    
    const newResume = new ResumeProfile({
      Title,
      Email: email
    });

    await newResume.save();
    res.status(201).json({ message: 'Resume created successfully', resume: newResume });

  } catch (err) {
    res.status(500).json({ message: 'Server error', error: err.message });
  }
};

const updateTitle = async (req, res) => {
  try {
    const { resumeId, newTitle } = req.body;
    const email = req.user.email;

    const existing = await ResumeProfile.findOne({ Email: email, Title: newTitle });
    if (existing) {
      return res.status(400).json({ message: 'You already have a resume with this title.' });
    }

    const updated = await ResumeProfile.findOneAndUpdate(
      { _id: resumeId, Email: email },
      { $set: { Title: newTitle } },
      { new: true }
    );

    if (!updated) return res.status(404).json({ message: 'Resume not found.' });

    res.status(200).json({ message: 'Title updated successfully', resume: updated });

  } catch (err) {
    res.status(500).json({ message: 'Server error', error: err.message });
  }
};

const updateTemplate = async (req, res) => {
  try {
    const { resumeId, templateName } = req.body;
    if (!resumeId || !templateName) {
      return res.status(400).json({ message: 'Resume ID and template name are required.' });
    }
    const updated = await ResumeProfile.findByIdAndUpdate(
      resumeId,
      { Template: templateName },
      { new: true }
    );

    if (!updated) return res.status(404).json({ message: 'Resume not found.' });

    res.status(200).json({ message: 'Template updated successfully', resume: updated });

  } catch (err) {
    res.status(500).json({ message: 'Server error', error: err.message });
  }
};

const togglePublication = async (req, res) => {
  try {
    const { resumeId, status } = req.body;

    const updated = await ResumeProfile.findByIdAndUpdate(
      resumeId,
      { isPublished: status },
      { new: true }
    );

    if (!updated) return res.status(404).json({ message: 'Resume not found.' });

    res.status(200).json({ message: `Resume ${status ? 'published' : 'unpublished'}`, resume: updated });

  } catch (err) {
    res.status(500).json({ message: 'Server error', error: err.message });
  }
};

const getPublishURL = async (req, res) => {
  try {
    const { resumeId } = req.params;

    const resume = await ResumeProfile.findById(resumeId);
    if (!resume) return res.status(404).json({ message: 'Resume not found.' });

    const baseUrl = 'https://yourdomain.com/resume'; // Customize this
    const fullUrl = `${baseUrl}/${resume._id}`;

    res.status(200).json({ message: 'Publish URL retrieved', url: fullUrl });

  } catch (err) {
    res.status(500).json({ message: 'Server error', error: err.message });
  }
};

const togglePublicAccess = async (req, res) => {
  try {
    const { resumeId, status } = req.body;

    const updated = await ResumeProfile.findByIdAndUpdate(
      resumeId,
      { PublicAccess: status },
      { new: true }
    );

    if (!updated) return res.status(404).json({ message: 'Resume not found.' });

    res.status(200).json({ message: `Public access ${status ? 'enabled' : 'disabled'}`, resume: updated });

  } catch (err) {
    res.status(500).json({ message: 'Server error', error: err.message });
  }
};

const updatePDFLink = async (req, res) => {
  try {
    const { resumeId, pdfLink } = req.body;

    const updated = await ResumeProfile.findByIdAndUpdate(
      resumeId,
      { PDFLink: pdfLink },
      { new: true }
    );

    if (!updated) return res.status(404).json({ message: 'Resume not found.' });

    res.status(200).json({ message: 'PDF export link updated', resume: updated });

  } catch (err) {
    res.status(500).json({ message: 'Server error', error: err.message });
  }
};

module.exports = {
  createResume,
  updateTitle,
  updateTemplate,
  togglePublication,
  getPublishURL,
  togglePublicAccess,
  updatePDFLink
};

