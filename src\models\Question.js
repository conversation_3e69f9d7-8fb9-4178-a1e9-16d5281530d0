const mongoose = require('mongoose');

const QuestionSchema = new mongoose.Schema({
    companyId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Company',
        required: true
    },
    createdBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    updatedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    },
    questionText: {
        type: String,
        required: true,
        maxlength: 1000
    },
    questionType: {
        type: String,
        enum: ['MCQ', 'Multiple-Select', 'Short-Answer', 'Code'],
        default: 'MCQ'
    },
    category: {
        type: String,
        enum: ['Frontend', 'Backend', 'Full Stack', 'Data Science', 'DevOps', 'Mobile', 'UI/UX', 'QA', 'Aptitude', 'Logical', 'Other'],
        required: true
    },
    difficulty: {
        type: String,
        enum: ['Easy', 'Medium', 'Hard'],
        default: 'Medium'
    },
    options: [{
        text: String,
        isCorrect: {
            type: Boolean,
            default: false
        }
    }],
    correctAnswer: {
        type: String,
        default: null
    },
    explanation: {
        type: String,
        maxlength: 500
    },
    points: {
        type: Number,
        default: 1
    },
    isActive: {
        type: Boolean,
        default: true
    }
}, {
    timestamps: true
});

module.exports = mongoose.model('Question', QuestionSchema);
