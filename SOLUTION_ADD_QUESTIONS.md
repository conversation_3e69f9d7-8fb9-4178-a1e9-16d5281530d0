# Solution: Add Questions to Test API

## 🔍 Problem Analysis

**Error:** `{"error":"Question IDs array is required"}`

**Root Cause:** The API expects `questionIds` as an array in the request body, but it's either:
1. Not being sent as an array
2. Empty array
3. Missing from request body

## ✅ Correct API Usage

### Endpoint
```
POST /api/tests/687f67966fad6a0ea16667e0/questions
```

### Headers
```
Content-Type: application/json
Authorization: Bearer YOUR_AUTH_TOKEN
```

### Request Body (Correct Format)
```json
{
  "questionIds": ["687777f10b7ee9e83b238045"],
  "points": 5
}
```

## 🚀 Frontend Implementation

### 1. JavaScript Function
```javascript
async function addQuestionsToTest(testId, questionIds, points = 1) {
  try {
    // Validate inputs
    if (!testId) {
      throw new Error('Test ID is required');
    }
    
    if (!questionIds || !Array.isArray(questionIds) || questionIds.length === 0) {
      throw new Error('Question IDs must be a non-empty array');
    }

    const response = await fetch(`http://localhost:5000/api/tests/${testId}/questions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('authToken')}`
      },
      body: JSON.stringify({
        questionIds: questionIds,  // Must be array
        points: points
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('✅ Success:', data);
    return data;
  } catch (error) {
    console.error('❌ Error:', error.message);
    throw error;
  }
}
```

### 2. Usage with Your Data
```javascript
// Add single question
addQuestionsToTest(
  '687f67966fad6a0ea16667e0',    // Your test ID
  ['687777f10b7ee9e83b238045'],  // Your question ID as array
  5                              // Points
);

// Add multiple questions
addQuestionsToTest(
  '687f67966fad6a0ea16667e0',
  [
    '687777f10b7ee9e83b238045',
    'another-question-id',
    'third-question-id'
  ],
  3
);
```

### 3. React Component Example
```jsx
const AddQuestionsComponent = () => {
  const [testId, setTestId] = useState('687f67966fad6a0ea16667e0');
  const [selectedQuestions, setSelectedQuestions] = useState(['687777f10b7ee9e83b238045']);
  const [points, setPoints] = useState(5);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  const handleAddQuestions = async () => {
    setLoading(true);
    setMessage('');
    
    try {
      const result = await addQuestionsToTest(testId, selectedQuestions, points);
      setMessage(`✅ Success: ${result.message}`);
    } catch (error) {
      setMessage(`❌ Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <h3>Add Questions to Test</h3>
      
      <div>
        <label>Test ID:</label>
        <input 
          value={testId} 
          onChange={(e) => setTestId(e.target.value)}
          placeholder="Test ID"
        />
      </div>

      <div>
        <label>Question IDs (comma separated):</label>
        <input 
          value={selectedQuestions.join(', ')} 
          onChange={(e) => setSelectedQuestions(e.target.value.split(',').map(id => id.trim()))}
          placeholder="question-id-1, question-id-2"
        />
      </div>

      <div>
        <label>Points:</label>
        <input 
          type="number" 
          value={points} 
          onChange={(e) => setPoints(parseInt(e.target.value))}
          min="1" 
          max="10"
        />
      </div>

      <button onClick={handleAddQuestions} disabled={loading}>
        {loading ? 'Adding...' : 'Add Questions'}
      </button>

      {message && <div>{message}</div>}
    </div>
  );
};
```

## 🧪 Testing Methods

### Method 1: Using the HTML Test Page
1. Open `test_api.html` in your browser
2. Enter your auth token
3. Use the pre-filled test ID: `687f67966fad6a0ea16667e0`
4. Use the pre-filled question ID: `687777f10b7ee9e83b238045`
5. Click "Add Questions to Test"

### Method 2: Using cURL
```bash
curl -X POST "http://localhost:5000/api/tests/687f67966fad6a0ea16667e0/questions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_AUTH_TOKEN" \
  -d '{
    "questionIds": ["687777f10b7ee9e83b238045"],
    "points": 5
  }'
```

### Method 3: Using Postman
1. **Method:** POST
2. **URL:** `http://localhost:5000/api/tests/687f67966fad6a0ea16667e0/questions`
3. **Headers:**
   - `Content-Type: application/json`
   - `Authorization: Bearer YOUR_AUTH_TOKEN`
4. **Body (raw JSON):**
```json
{
  "questionIds": ["687777f10b7ee9e83b238045"],
  "points": 5
}
```

### Method 4: Browser Console
```javascript
// Set your auth token first
localStorage.setItem('authToken', 'YOUR_AUTH_TOKEN_HERE');

// Then run this function
fetch('http://localhost:5000/api/tests/687f67966fad6a0ea16667e0/questions', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${localStorage.getItem('authToken')}`
  },
  body: JSON.stringify({
    questionIds: ['687777f10b7ee9e83b238045'],
    points: 5
  })
})
.then(response => response.json())
.then(data => console.log('Success:', data))
.catch(error => console.error('Error:', error));
```

## 🔍 Debugging Tips

### 1. Check Request Format
Make sure your request body looks exactly like this:
```json
{
  "questionIds": ["687777f10b7ee9e83b238045"],
  "points": 5
}
```

### 2. Common Mistakes
❌ **Wrong:** `"questionIds": "687777f10b7ee9e83b238045"` (string instead of array)
❌ **Wrong:** `"questionIds": []` (empty array)
❌ **Wrong:** `"questionId": ["687777f10b7ee9e83b238045"]` (wrong property name)

✅ **Correct:** `"questionIds": ["687777f10b7ee9e83b238045"]` (array with elements)

### 3. Verify Your Data
- **Test ID:** `687f67966fad6a0ea16667e0` ✅
- **Question ID:** `687777f10b7ee9e83b238045` ✅
- **Company IDs match:** Your test company ID (`6876052fb509bcbdcd5d8a8e`) should match your question's company ID (`687753791d871dbfc74073f9`)

⚠️ **Note:** Your test and question have different company IDs. This might cause access issues. Make sure you're logged in as the correct company user.

## 📋 Expected Response

### Success Response
```json
{
  "success": true,
  "message": "1 questions added to test",
  "totalQuestions": 3
}
```

### Error Responses
```json
// Missing questionIds
{
  "error": "Question IDs array is required"
}

// Test not found
{
  "error": "Test not found or access denied"
}

// Question not accessible
{
  "error": "Some questions not found or not accessible"
}
```

## 🎯 Next Steps

1. **Test the API** using one of the methods above
2. **Verify company access** - ensure your auth token belongs to the correct company
3. **Check question ownership** - make sure the question belongs to your company
4. **Implement in your frontend** using the provided React component example

The key is ensuring `questionIds` is always sent as an array, even for a single question!
